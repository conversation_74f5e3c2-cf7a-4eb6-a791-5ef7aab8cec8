#!/bin/bash

# PHPStan - Static Analysis Tool
# This script runs PHPStan static analysis on the codebase

set -e

echo "🔍 Running PHPStan Static Analysis..."
echo "📊 Analyzing code for potential bugs and issues"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if phpstan is available
if ! command -v ./vendor/bin/phpstan &> /dev/null; then
    echo -e "${RED}❌ PHPStan not found!${NC}"
    echo "💡 Install it with: composer install"
    exit 1
fi

# Show help
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  --level=X     Set analysis level (0-9, default: 6)"
    echo "  --baseline    Generate baseline file"
    echo "  --clear-cache Clear PHPStan cache"
    echo "  --memory=XG   Set memory limit (default: 1G)"
    echo "  --help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Standard analysis"
    echo "  $0 --level=8          # Higher analysis level"
    echo "  $0 --baseline         # Generate baseline"
    echo "  $0 --clear-cache      # Clear cache and run"
    echo "  $0 --memory=2G        # Use more memory"
    exit 0
fi

# Parse arguments
LEVEL=""
MEMORY="1G"
BASELINE=false
CLEAR_CACHE=false

for arg in "$@"; do
    case $arg in
        --level=*)
            LEVEL="${arg#*=}"
            shift
            ;;
        --memory=*)
            MEMORY="${arg#*=}"
            shift
            ;;
        --baseline)
            BASELINE=true
            shift
            ;;
        --clear-cache)
            CLEAR_CACHE=true
            shift
            ;;
    esac
done

# Clear cache if requested
if [ "$CLEAR_CACHE" = true ]; then
    echo "🗑️  Clearing PHPStan cache..."
    ./vendor/bin/phpstan clear-result-cache
    echo ""
fi

# Generate baseline if requested
if [ "$BASELINE" = true ]; then
    echo "📝 Generating PHPStan baseline..."
    ./vendor/bin/phpstan analyse --generate-baseline --memory-limit=$MEMORY
    echo ""
    echo -e "${GREEN}✅ Baseline generated successfully!${NC}"
    echo "💡 This will ignore current errors. Fix them gradually and regenerate baseline."
    exit 0
fi

# Build command
CMD="./vendor/bin/phpstan analyse --memory-limit=$MEMORY"

if [ -n "$LEVEL" ]; then
    CMD="$CMD --level=$LEVEL"
    echo "📊 Using analysis level: $LEVEL"
fi

echo "🚀 Running PHPStan analysis..."
echo "💾 Memory limit: $MEMORY"
echo ""

# Run PHPStan
if eval $CMD; then
    echo ""
    echo -e "${GREEN}✅ PHPStan analysis completed successfully!${NC}"
    echo "🎉 No errors found in your code."
else
    echo ""
    echo -e "${RED}❌ PHPStan found issues in your code!${NC}"
    echo ""
    echo -e "${YELLOW}💡 Tips for fixing PHPStan errors:${NC}"
    echo "   • Add proper type hints to methods and properties"
    echo "   • Use @var, @param, @return annotations"
    echo "   • Handle null values properly"
    echo "   • Use strict comparison operators (=== instead of ==)"
    echo ""
    echo -e "${YELLOW}💡 Run '$0 --baseline' to create a baseline and fix errors gradually.${NC}"
    exit 1
fi
