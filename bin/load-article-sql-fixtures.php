#!/usr/bin/env php
<?php

declare(strict_types=1);

require __DIR__ . '/../app/bootstrap.php';

/**
 * Load Article SQL fixtures directly into SQLite database
 * This script executes the SQL fixture files directly for articles
 */

try {
    // Get database path from settings
    $containerBuilder = new \DI\ContainerBuilder();
    $settings = require __DIR__ . '/../app/settings.php';
    $settings($containerBuilder);
    $container = $containerBuilder->build();

    $settingsObj = $container->get(\App\Application\Settings\SettingsInterface::class);
    $doctrineSettings = $settingsObj->get('doctrine');
    $dbPath = $doctrineSettings['connections']['article']['path'];

    echo "📁 Article database path: $dbPath\n";

    // Check if database file exists
    if (!file_exists($dbPath)) {
        echo "❌ Article database file does not exist. Please run schema creation first.\n";
        echo "💡 Run: php bin/setup-article-database.php\n";
        exit(1);
    }

    // Connect to SQLite database
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "🔗 Connected to Article SQLite database\n";

    // Read SQL fixture file
    $sqlFile = getAppPaths()->path('fixtures/articles.sql');

    if (!file_exists($sqlFile)) {
        echo "❌ Article SQL fixture file not found: $sqlFile\n";
        exit(1);
    }

    $sql = file_get_contents($sqlFile);
    echo "📄 Loaded SQL from: $sqlFile\n";

    // Execute SQL statements
    echo "⚡ Executing SQL statements...\n";

    // Split SQL into individual statements and execute
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function ($stmt) {
            return !empty($stmt) && !str_starts_with($stmt, '--');
        }
    );

    foreach ($statements as $statement) {
        if (!empty($statement)) {
            $pdo->exec($statement);
        }
    }

    // Verify data was loaded
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM article");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $articleCount = $result['count'];

    echo "✅ Article SQL fixtures loaded successfully!\n";
    echo "🎨 Total articles in database: $articleCount\n";

    // Show sample data by type
    echo "\n📊 Sample articles by type:\n";
    $stmt = $pdo->query("
        SELECT type, COUNT(*) as count, 
               GROUP_CONCAT(title, ' | ') as sample_titles 
        FROM article 
        GROUP BY type 
        ORDER BY type
    ");
    $typeStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($typeStats as $stat) {
        $sampleTitles = substr($stat['sample_titles'], 0, 100) . (strlen($stat['sample_titles']) > 100 ? '...' : '');
        echo "   {$stat['type']}: {$stat['count']} articles\n";
        echo "     Examples: {$sampleTitles}\n\n";
    }

    echo "🎉 Article database is ready for testing!\n";
    echo "📊 You can now test the Article API endpoints:\n";
    echo "   - GET /articles (list all articles)\n";
    echo "   - GET /article/1 (view specific article)\n";
    echo "   - GET /article/sku/DIG-ABS-001 (view article by SKU)\n";
} catch (Exception $e) {
    echo "❌ Error loading Article SQL fixtures: " . $e->getMessage() . "\n";
    exit(1);
}
