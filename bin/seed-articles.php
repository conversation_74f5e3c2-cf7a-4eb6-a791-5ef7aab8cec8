#!/usr/bin/env php
<?php

declare(strict_types=1);

use App\Domain\Article\Article;
use App\Domain\Article\ArticleType;
use Doctrine\ORM\EntityManager;

require __DIR__ . '/../app/bootstrap.php';

/**
 * Database seeder for Article entities
 * This script populates the article database with test article data
 */

try {
    /** @var EntityManager $entityManager */
    $entityManager = $container->get('ArticleEntityManager');

    echo "🌱 Starting article data seeding...\n";

    // Check if articles already exist
    $existingArticles = $entityManager->getRepository(Article::class)->findAll();
    if (count($existingArticles) > 0) {
        echo "⚠️  Articles already exist in database. Do you want to clear and reseed? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 'y') {
            echo "❌ Seeding cancelled.\n";
            exit(0);
        }
        
        // Clear existing articles
        echo "🗑️  Clearing existing articles...\n";
        foreach ($existingArticles as $article) {
            $entityManager->remove($article);
        }
        $entityManager->flush();
    }

    // Test articles data
    $articlesData = [
        [
            'title' => 'Abstract Digital Masterpiece',
            'type' => ArticleType::DIGITAL_ART,
            'sku' => 'DIG-ABS-001',
            'description' => 'A stunning abstract digital artwork created with modern techniques.',
            'price' => 299.99,
            'currency' => 'EUR',
            'deliveryMethod' => 'Digital Download',
            'deliveryTimeDays' => 1,
            'stockQuantity' => 100
        ],
        [
            'title' => 'Vintage Oil Painting - Landscape',
            'type' => ArticleType::PAINTING,
            'sku' => 'PAINT-VIN-002',
            'description' => 'Beautiful vintage landscape oil painting from the 19th century.',
            'price' => 1299.50,
            'currency' => 'EUR',
            'deliveryMethod' => 'Courier',
            'deliveryTimeDays' => 7,
            'stockQuantity' => 1
        ],
        [
            'title' => 'Modern Sculpture - Bronze',
            'type' => ArticleType::SCULPTURE,
            'sku' => 'SCULP-MOD-003',
            'description' => 'Contemporary bronze sculpture by emerging artist.',
            'price' => 2499.00,
            'currency' => 'EUR',
            'deliveryMethod' => 'Special Delivery',
            'deliveryTimeDays' => 14,
            'stockQuantity' => 3
        ],
        [
            'title' => 'Photography Series - Urban Life',
            'type' => ArticleType::PHOTOGRAPHY,
            'sku' => 'PHOTO-URB-004',
            'description' => 'Limited edition photography prints capturing urban life.',
            'price' => 199.99,
            'currency' => 'EUR',
            'deliveryMethod' => 'Standard Post',
            'deliveryTimeDays' => 5,
            'stockQuantity' => 25
        ],
        [
            'title' => 'Art History Blog Post - Renaissance',
            'type' => ArticleType::BLOG_ART,
            'sku' => 'BLOG-REN-005',
            'description' => 'In-depth article about Renaissance art techniques and masters.',
            'price' => null,
            'currency' => 'EUR',
            'deliveryMethod' => null,
            'deliveryTimeDays' => null,
            'stockQuantity' => 999
        ],
        [
            'title' => 'Hand-drawn Portrait Commission',
            'type' => ArticleType::DRAWING,
            'sku' => 'DRAW-PORT-006',
            'description' => 'Custom hand-drawn portrait in charcoal or pencil.',
            'price' => 399.00,
            'currency' => 'EUR',
            'deliveryMethod' => 'Registered Post',
            'deliveryTimeDays' => 21,
            'stockQuantity' => 10
        ],
        [
            'title' => 'Mixed Media Collage',
            'type' => ArticleType::MIXED_MEDIA,
            'sku' => 'MIX-COL-007',
            'description' => 'Unique mixed media collage combining various materials.',
            'price' => 599.99,
            'currency' => 'EUR',
            'deliveryMethod' => 'Courier',
            'deliveryTimeDays' => 10,
            'stockQuantity' => 5
        ],
        [
            'title' => 'Collectible Art Print - Limited Edition',
            'type' => ArticleType::COLLECTIBLE,
            'sku' => 'COLL-LIM-008',
            'description' => 'Limited edition art print, numbered and signed.',
            'price' => 149.99,
            'currency' => 'EUR',
            'deliveryMethod' => 'Standard Post',
            'deliveryTimeDays' => 3,
            'stockQuantity' => 50
        ],
        [
            'title' => 'Vintage Poster Collection',
            'type' => ArticleType::VINTAGE,
            'sku' => 'VINT-POST-009',
            'description' => 'Collection of authentic vintage advertising posters.',
            'price' => 899.00,
            'currency' => 'EUR',
            'deliveryMethod' => 'Special Delivery',
            'deliveryTimeDays' => 7,
            'stockQuantity' => 2
        ],
        [
            'title' => 'Digital Art Tutorial Series',
            'type' => ArticleType::DIGITAL_ART,
            'sku' => 'DIG-TUT-010',
            'description' => 'Complete digital art tutorial series with video content.',
            'price' => 99.99,
            'currency' => 'EUR',
            'deliveryMethod' => 'Digital Download',
            'deliveryTimeDays' => 1,
            'stockQuantity' => 500
        ]
    ];

    echo "🎨 Creating " . count($articlesData) . " test articles...\n";

    foreach ($articlesData as $index => $articleData) {
        $article = new Article(
            null, // ID will be auto-generated
            $articleData['title'],
            $articleData['type'],
            $articleData['sku'],
            $articleData['description'],
            $articleData['price'],
            $articleData['currency'],
            $articleData['deliveryMethod'],
            $articleData['deliveryTimeDays'],
            $articleData['stockQuantity']
        );

        $entityManager->persist($article);
        echo "✅ Created article: {$articleData['sku']} - {$articleData['title']}\n";
    }

    // Flush all changes to database
    $entityManager->flush();

    echo "\n🎉 Successfully seeded " . count($articlesData) . " articles!\n";
    echo "📊 You can now test the Article API endpoints:\n";
    echo "   - GET /articles (list all articles)\n";
    echo "   - GET /article/1 (view specific article)\n";
    echo "   - GET /article/sku/DIG-ABS-001 (view article by SKU)\n";

} catch (Exception $e) {
    echo "❌ Error seeding articles: " . $e->getMessage() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
