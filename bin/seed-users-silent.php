#!/usr/bin/env php
<?php

declare(strict_types=1);

use App\Domain\User\User;
use Doctrine\ORM\EntityManager;

require __DIR__ . '/../app/bootstrap.php';

/**
 * Silent database seeder for User entities
 * This script populates the database with test user data without prompts
 * Useful for automated testing and CI/CD
 */

try {
    /** @var EntityManager $entityManager */
    $entityManager = $container->get(EntityManager::class);

    // Clear existing users
    $existingUsers = $entityManager->getRepository(User::class)->findAll();
    foreach ($existingUsers as $user) {
        $entityManager->remove($user);
    }
    $entityManager->flush();

    // Test users data
    $usersData = [
        ['username' => 'john.doe', 'firstName' => 'John', 'lastName' => 'Doe'],
        ['username' => 'jane.smith', 'firstName' => '<PERSON>', 'lastName' => '<PERSON>'],
        ['username' => 'bob.wilson', 'firstName' => '<PERSON>', 'lastName' => '<PERSON>'],
        ['username' => 'alice.johnson', 'firstName' => '<PERSON>', 'lastName' => 'Johnson'],
        ['username' => 'charlie.brown', 'firstName' => 'Charlie', 'lastName' => 'Brown'],
        ['username' => 'diana.prince', 'firstName' => 'Diana', 'lastName' => 'Prince'],
        ['username' => 'peter.parker', 'firstName' => 'Peter', 'lastName' => 'Parker'],
        ['username' => 'mary.jane', 'firstName' => 'Mary', 'lastName' => 'Jane'],
        ['username' => 'bruce.wayne', 'firstName' => 'Bruce', 'lastName' => 'Wayne'],
        ['username' => 'clark.kent', 'firstName' => 'Clark', 'lastName' => 'Kent']
    ];

    foreach ($usersData as $userData) {
        $user = new User(
            null,
            $userData['username'],
            $userData['firstName'],
            $userData['lastName']
        );
        $entityManager->persist($user);
    }

    $entityManager->flush();

    echo "Seeded " . count($usersData) . " users successfully.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
