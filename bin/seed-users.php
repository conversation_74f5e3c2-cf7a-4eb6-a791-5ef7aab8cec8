#!/usr/bin/env php
<?php

declare(strict_types=1);

use App\Domain\User\User;
use Doctrine\ORM\EntityManager;

require __DIR__ . '/../app/bootstrap.php';

/**
 * Database seeder for User entities
 * This script populates the database with test user data
 */

try {
    /** @var EntityManager $entityManager */
    $entityManager = $container->get(EntityManager::class);

    echo "🌱 Starting user data seeding...\n";

    // Check if users already exist
    $existingUsers = $entityManager->getRepository(User::class)->findAll();
    if (count($existingUsers) > 0) {
        echo "⚠️  Users already exist in database. Do you want to clear and reseed? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);

        if (trim(strtolower($line)) !== 'y') {
            echo "❌ Seeding cancelled.\n";
            exit(0);
        }

        // Clear existing users
        echo "🗑️  Clearing existing users...\n";
        foreach ($existingUsers as $user) {
            $entityManager->remove($user);
        }
        $entityManager->flush();
    }

    // Test users data
    $usersData = [
        [
            'username' => 'john.doe',
            'firstName' => 'John',
            'lastName' => 'Doe'
        ],
        [
            'username' => 'jane.smith',
            'firstName' => 'Jane',
            'lastName' => 'Smith'
        ],
        [
            'username' => 'bob.wilson',
            'firstName' => 'Bob',
            'lastName' => 'Wilson'
        ],
        [
            'username' => 'alice.johnson',
            'firstName' => 'Alice',
            'lastName' => 'Johnson'
        ],
        [
            'username' => 'charlie.brown',
            'firstName' => 'Charlie',
            'lastName' => 'Brown'
        ],
        [
            'username' => 'diana.prince',
            'firstName' => 'Diana',
            'lastName' => 'Prince'
        ],
        [
            'username' => 'peter.parker',
            'firstName' => 'Peter',
            'lastName' => 'Parker'
        ],
        [
            'username' => 'mary.jane',
            'firstName' => 'Mary',
            'lastName' => 'Jane'
        ],
        [
            'username' => 'bruce.wayne',
            'firstName' => 'Bruce',
            'lastName' => 'Wayne'
        ],
        [
            'username' => 'clark.kent',
            'firstName' => 'Clark',
            'lastName' => 'Kent'
        ]
    ];

    echo "👥 Creating " . count($usersData) . " test users...\n";

    foreach ($usersData as $index => $userData) {
        $user = new User(
            null, // ID will be auto-generated
            $userData['username'],
            $userData['firstName'],
            $userData['lastName']
        );

        $entityManager->persist($user);
        echo "✅ Created user: {$userData['username']} ({$userData['firstName']} {$userData['lastName']})\n";
    }

    // Flush all changes to database
    $entityManager->flush();

    echo "\n🎉 Successfully seeded " . count($usersData) . " users!\n";
    echo "📊 You can now test the API endpoints:\n";
    echo "   - GET /users (list all users)\n";
    echo "   - GET /user/1 (view specific user)\n";
} catch (Exception $e) {
    echo "❌ Error seeding users: " . $e->getMessage() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
