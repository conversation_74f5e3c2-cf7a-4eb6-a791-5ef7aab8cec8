<?php

declare(strict_types=1);

require __DIR__ . '/../app/bootstrap.php';

/**
 * Load SQL fixtures directly into SQLite database
 * This script executes the SQL fixture files directly
 */

try {
    // Get database path from settings
    $settings = require __DIR__ . '/../app/settings.php';
    $containerBuilder = new \DI\ContainerBuilder();
    $settings($containerBuilder);
    $container = $containerBuilder->build();
    
    $doctrineSettings = $container->get('settings')['doctrine'];
    $dbPath = $doctrineSettings['connections']['default']['path'];

    echo "📁 Database path: $dbPath\n";

    // Check if database file exists
    if (!file_exists($dbPath)) {
        echo "❌ Database file does not exist. Please run schema creation first.\n";
        echo "💡 Run: php bin/doctrine.php orm:schema-tool:create\n";
        exit(1);
    }

    // Connect to SQLite database
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "🔗 Connected to SQLite database\n";

    // Read SQL fixture file
    $sqlFile = __DIR__ . '/../fixtures/users.sql';
    
    if (!file_exists($sqlFile)) {
        echo "❌ SQL fixture file not found: $sqlFile\n";
        exit(1);
    }

    $sql = file_get_contents($sqlFile);
    echo "📄 Loaded SQL from: $sqlFile\n";

    // Execute SQL statements
    echo "⚡ Executing SQL statements...\n";
    
    // Split SQL into individual statements and execute
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !str_starts_with($stmt, '--');
        }
    );

    foreach ($statements as $statement) {
        if (!empty($statement)) {
            $pdo->exec($statement);
        }
    }

    // Verify data was loaded
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM user");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $userCount = $result['count'];

    echo "✅ SQL fixtures loaded successfully!\n";
    echo "👥 Total users in database: $userCount\n";

    // Show sample data
    echo "\n📊 Sample users:\n";
    $stmt = $pdo->query("SELECT id, username, first_name, last_name FROM user ORDER BY id LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($users as $user) {
        echo "   ID: {$user['id']} | {$user['username']} | {$user['first_name']} {$user['last_name']}\n";
    }

    echo "\n🎉 Database is ready for testing!\n";

} catch (Exception $e) {
    echo "❌ Error loading SQL fixtures: " . $e->getMessage() . "\n";
    exit(1);
}
