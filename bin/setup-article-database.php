#!/usr/bin/env php
<?php

declare(strict_types=1);

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Tools\SchemaTool;

require __DIR__ . '/../app/bootstrap.php';

/**
 * Complete Article database setup script
 * This script creates the article database schema and optionally seeds test data
 */

try {
    /** @var EntityManager $entityManager */
    $entityManager = $container->get('ArticleEntityManager');

    echo "🔧 Setting up Article database...\n";

    // Get all entity metadata for Article domain
    $metadatas = $entityManager->getMetadataFactory()->getAllMetadata();

    if (empty($metadatas)) {
        echo "❌ No Article entity metadata found!\n";
        exit(1);
    }

    // Create schema tool
    $schemaTool = new SchemaTool($entityManager);

    echo "📋 Found " . count($metadatas) . " Article entities\n";

    // Check if we should drop existing schema
    echo "🗑️  Drop existing Article schema? (y/N): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);

    if (trim(strtolower($line)) === 'y') {
        echo "🗑️  Dropping existing Article schema...\n";
        $schemaTool->dropSchema($metadatas);
    }

    // Create schema
    echo "🏗️  Creating Article database schema...\n";
    $schemaTool->createSchema($metadatas);
    echo "✅ Article database schema created successfully!\n";

    // Ask if user wants to seed data
    echo "\n🌱 Seed test article data? (Y/n): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);

    if (trim(strtolower($line)) !== 'n') {
        echo "🌱 Seeding test article data...\n";

        // Include and run the seeder
        require __DIR__ . '/seed-articles.php';
    }

    echo "\n🎉 Article database setup completed successfully!\n";
    echo "📊 Your article database is ready for use.\n";
} catch (Exception $e) {
    echo "❌ Error setting up Article database: " . $e->getMessage() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
