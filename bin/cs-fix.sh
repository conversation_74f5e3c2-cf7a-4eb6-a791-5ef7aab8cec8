#!/bin/bash

# PHP Code Beautifier and Fixer - Fix coding standards
# This script automatically fixes coding standard violations

set -e

echo "🔧 Running PHP Code Beautifier and Fixer..."
echo "🛠️  Automatically fixing coding standards violations"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if phpcbf is available
if ! command -v ./vendor/bin/phpcbf &> /dev/null; then
    echo -e "${RED}❌ PHP Code Beautifier and Fixer not found!${NC}"
    echo "💡 Install it with: composer install"
    exit 1
fi

# Show help
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  --dry-run  Show what would be fixed without making changes"
    echo "  --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0           # Fix all issues"
    echo "  $0 --dry-run # Show what would be fixed"
    exit 0
fi

# Dry run mode
if [ "$1" = "--dry-run" ]; then
    echo -e "${BLUE}🔍 Dry run mode - showing what would be fixed...${NC}"
    echo ""
    ./vendor/bin/phpcs --standard=phpcs.xml --report=diff
    echo ""
    echo -e "${YELLOW}💡 Run '$0' without --dry-run to actually fix these issues.${NC}"
    exit 0
fi

# Check current status first
echo "📊 Checking current coding standards status..."
if ./vendor/bin/phpcs --standard=phpcs.xml --report=summary; then
    echo ""
    echo -e "${GREEN}✅ No coding standards violations found!${NC}"
    echo "🎉 Your code already follows all coding standards."
    exit 0
fi

echo ""
echo "🔧 Fixing coding standards violations..."

# Run phpcbf to fix issues
if ./vendor/bin/phpcbf --standard=phpcs.xml; then
    echo ""
    echo -e "${GREEN}✅ Coding standards violations have been fixed!${NC}"
    
    # Check if there are any remaining issues
    echo ""
    echo "🔍 Checking for any remaining issues..."
    if ./vendor/bin/phpcs --standard=phpcs.xml --report=summary; then
        echo ""
        echo -e "${GREEN}🎉 All coding standards issues have been resolved!${NC}"
    else
        echo ""
        echo -e "${YELLOW}⚠️  Some issues require manual fixing:${NC}"
        ./vendor/bin/phpcs --standard=phpcs.xml
        echo ""
        echo -e "${YELLOW}💡 Please review and fix the remaining issues manually.${NC}"
        exit 1
    fi
else
    echo ""
    echo -e "${YELLOW}⚠️  Some files were fixed, but there might be remaining issues.${NC}"
    echo "🔍 Running final check..."
    
    if ./vendor/bin/phpcs --standard=phpcs.xml; then
        echo ""
        echo -e "${GREEN}✅ All issues have been resolved!${NC}"
    else
        echo ""
        echo -e "${YELLOW}💡 Some issues require manual attention.${NC}"
        exit 1
    fi
fi
