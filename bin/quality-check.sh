#!/bin/bash

# Complete Quality Check Script
# This script runs all quality checks: coding standards, static analysis, and tests

set -e

echo "🎯 Running Complete Quality Check..."
echo "📋 This will run: Code Standards + Static Analysis + Tests"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Track results
CS_RESULT=0
PHPSTAN_RESULT=0
TEST_RESULT=0

# Show help
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  --fix         Run cs-fix before checking"
    echo "  --coverage    Run tests with coverage"
    echo "  --fast        Skip tests (only CS and PHPStan)"
    echo "  --help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0            # Full quality check"
    echo "  $0 --fix      # Fix CS issues first, then check"
    echo "  $0 --fast     # Quick check without tests"
    echo "  $0 --coverage # Include test coverage"
    exit 0
fi

# Parse arguments
FIX_CS=false
WITH_COVERAGE=false
SKIP_TESTS=false

for arg in "$@"; do
    case $arg in
        --fix)
            FIX_CS=true
            shift
            ;;
        --coverage)
            WITH_COVERAGE=true
            shift
            ;;
        --fast)
            SKIP_TESTS=true
            shift
            ;;
    esac
done

echo "🚀 Starting quality checks..."
echo ""

# Step 1: Fix coding standards if requested
if [ "$FIX_CS" = true ]; then
    echo -e "${BLUE}🔧 Step 0: Fixing coding standards...${NC}"
    if ./bin/cs-fix.sh; then
        echo -e "${GREEN}✅ Coding standards fixed${NC}"
    else
        echo -e "${YELLOW}⚠️  Some CS issues need manual fixing${NC}"
    fi
    echo ""
fi

# Step 1: Check coding standards
echo -e "${BLUE}🔍 Step 1: Checking coding standards...${NC}"
if ./bin/cs-check.sh; then
    echo -e "${GREEN}✅ Coding standards: PASSED${NC}"
    CS_RESULT=0
else
    echo -e "${RED}❌ Coding standards: FAILED${NC}"
    CS_RESULT=1
fi
echo ""

# Step 2: Run static analysis
echo -e "${BLUE}🔍 Step 2: Running static analysis...${NC}"
if ./bin/phpstan.sh; then
    echo -e "${GREEN}✅ Static analysis: PASSED${NC}"
    PHPSTAN_RESULT=0
else
    echo -e "${RED}❌ Static analysis: FAILED${NC}"
    PHPSTAN_RESULT=1
fi
echo ""

# Step 3: Run tests (unless skipped)
if [ "$SKIP_TESTS" = false ]; then
    echo -e "${BLUE}🧪 Step 3: Running tests...${NC}"
    if [ "$WITH_COVERAGE" = true ]; then
        if composer test-coverage; then
            echo -e "${GREEN}✅ Tests with coverage: PASSED${NC}"
            TEST_RESULT=0
        else
            echo -e "${RED}❌ Tests with coverage: FAILED${NC}"
            TEST_RESULT=1
        fi
    else
        if composer test; then
            echo -e "${GREEN}✅ Tests: PASSED${NC}"
            TEST_RESULT=0
        else
            echo -e "${RED}❌ Tests: FAILED${NC}"
            TEST_RESULT=1
        fi
    fi
    echo ""
else
    echo -e "${YELLOW}⏭️  Step 3: Tests skipped (--fast mode)${NC}"
    echo ""
fi

# Summary
echo "📊 Quality Check Summary:"
echo "========================"

if [ $CS_RESULT -eq 0 ]; then
    echo -e "Coding Standards: ${GREEN}✅ PASSED${NC}"
else
    echo -e "Coding Standards: ${RED}❌ FAILED${NC}"
fi

if [ $PHPSTAN_RESULT -eq 0 ]; then
    echo -e "Static Analysis:  ${GREEN}✅ PASSED${NC}"
else
    echo -e "Static Analysis:  ${RED}❌ FAILED${NC}"
fi

if [ "$SKIP_TESTS" = false ]; then
    if [ $TEST_RESULT -eq 0 ]; then
        echo -e "Tests:            ${GREEN}✅ PASSED${NC}"
    else
        echo -e "Tests:            ${RED}❌ FAILED${NC}"
    fi
fi

echo ""

# Overall result
TOTAL_FAILED=$((CS_RESULT + PHPSTAN_RESULT + TEST_RESULT))

if [ $TOTAL_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All quality checks PASSED!${NC}"
    echo "✨ Your code is ready for production!"
    exit 0
else
    echo -e "${RED}❌ $TOTAL_FAILED quality check(s) FAILED!${NC}"
    echo ""
    echo -e "${YELLOW}💡 Quick fixes:${NC}"
    if [ $CS_RESULT -ne 0 ]; then
        echo "   • Run './bin/cs-fix.sh' to fix coding standards"
    fi
    if [ $PHPSTAN_RESULT -ne 0 ]; then
        echo "   • Fix PHPStan errors or run './bin/phpstan.sh --baseline'"
    fi
    if [ $TEST_RESULT -ne 0 ]; then
        echo "   • Fix failing tests or update test expectations"
    fi
    exit 1
fi
