#!/bin/bash

# PHP Code Sniffer - Check coding standards
# This script checks if the code follows PSR-12 and additional coding standards

set -e

echo "🔍 Running PHP Code Sniffer..."
echo "📋 Checking coding standards (PSR-12 + custom rules)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if phpcs is available
if ! command -v ./vendor/bin/phpcs &> /dev/null; then
    echo -e "${RED}❌ PHP Code Sniffer not found!${NC}"
    echo "💡 Install it with: composer install"
    exit 1
fi

# Run phpcs with different output formats based on arguments
if [ "$1" = "--diff" ]; then
    echo "📊 Showing diff format..."
    ./vendor/bin/phpcs --standard=phpcs.xml --report=diff
elif [ "$1" = "--summary" ]; then
    echo "📊 Showing summary..."
    ./vendor/bin/phpcs --standard=phpcs.xml --report=summary
elif [ "$1" = "--json" ]; then
    echo "📊 Showing JSON format..."
    ./vendor/bin/phpcs --standard=phpcs.xml --report=json
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  --diff     Show diff format (what needs to be changed)"
    echo "  --summary  Show summary report"
    echo "  --json     Show JSON format"
    echo "  --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Standard check"
    echo "  $0 --diff       # Show what needs to be fixed"
    echo "  $0 --summary    # Show summary of issues"
    exit 0
else
    # Standard check
    if ./vendor/bin/phpcs --standard=phpcs.xml; then
        echo ""
        echo -e "${GREEN}✅ All coding standards checks passed!${NC}"
        echo "🎉 Your code follows PSR-12 and custom coding standards."
    else
        echo ""
        echo -e "${RED}❌ Coding standards violations found!${NC}"
        echo -e "${YELLOW}💡 Run './bin/cs-fix.sh' to automatically fix most issues.${NC}"
        echo -e "${YELLOW}💡 Run '$0 --diff' to see what needs to be changed.${NC}"
        exit 1
    fi
fi
