#!/usr/bin/env php
<?php

declare(strict_types=1);

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Tools\SchemaTool;

require __DIR__ . '/../app/bootstrap.php';

/**
 * Complete database setup script
 * This script creates the database schema and optionally seeds test data
 */

try {
    /** @var EntityManager $entityManager */
    $entityManager = $container->get(EntityManager::class);

    echo "🔧 Setting up database...\n";

    // Get all entity metadata
    $metadatas = $entityManager->getMetadataFactory()->getAllMetadata();

    if (empty($metadatas)) {
        echo "❌ No entity metadata found!\n";
        exit(1);
    }

    // Create schema tool
    $schemaTool = new SchemaTool($entityManager);

    echo "📋 Found " . count($metadatas) . " entities\n";

    // Check if we should drop existing schema
    echo "🗑️  Drop existing schema? (y/N): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);

    if (trim(strtolower($line)) === 'y') {
        echo "🗑️  Dropping existing schema...\n";
        $schemaTool->dropSchema($metadatas);
    }

    // Create schema
    echo "🏗️  Creating database schema...\n";
    $schemaTool->createSchema($metadatas);
    echo "✅ Database schema created successfully!\n";

    // Ask if user wants to seed data
    echo "\n🌱 Seed test user data? (Y/n): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);

    if (trim(strtolower($line)) !== 'n') {
        echo "🌱 Seeding test data...\n";

        // Include and run the seeder
        require __DIR__ . '/seed-users.php';
    }

    echo "\n🎉 Database setup completed successfully!\n";
    echo "📊 Your database is ready for use.\n";
} catch (Exception $e) {
    echo "❌ Error setting up database: " . $e->getMessage() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
