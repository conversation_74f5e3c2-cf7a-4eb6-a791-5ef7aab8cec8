-- SQLite test data for users table
-- This file contains sample user data for testing purposes

-- Clear existing data (optional)
DELETE FROM user;

-- Reset auto-increment counter
DELETE FROM sqlite_sequence WHERE name='user';

-- Insert test users
INSERT INTO user (username, first_name, last_name) VALUES
('john.doe', '<PERSON>', '<PERSON><PERSON>'),
('jane.smith', '<PERSON>', '<PERSON>'),
('bob.wilson', '<PERSON>', '<PERSON>'),
('alice.johnson', '<PERSON>', '<PERSON>'),
('charlie.brown', '<PERSON>', '<PERSON>'),
('diana.prince', '<PERSON>', '<PERSON>'),
('peter.parker', '<PERSON>', '<PERSON>'),
('mary.jane', '<PERSON>', '<PERSON>'),
('bruce.wayne', '<PERSON>', '<PERSON>'),
('clark.kent', '<PERSON>', '<PERSON>'),
('tony.stark', '<PERSON>', '<PERSON>'),
('steve.rogers', '<PERSON>', '<PERSON>'),
('natasha.romanoff', '<PERSON>', '<PERSON><PERSON>'),
('thor.odin<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'),
('bruce.banner', '<PERSON>', '<PERSON>'),
('wanda.maximoff', '<PERSON>', '<PERSON>off'),
('vision.android', 'Vision', 'Android'),
('scott.lang', 'Scott', 'Lang'),
('hope.pym', 'Hope', 'Pym'),
('carol.danvers', 'Carol', 'Danvers');

-- Verify the data was inserted
-- SELECT COUNT(*) as total_users FROM user;
-- SELECT * FROM user ORDER BY id LIMIT 5;
