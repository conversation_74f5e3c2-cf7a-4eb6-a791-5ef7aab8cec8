# Database Fixtures and Test Data

This directory contains database fixtures and scripts for setting up test data in your Slim 4 + Doctrine 3 application.

## Available Scripts

### 1. Complete Database Setup
```bash
php bin/setup-database.php
```
- Creates database schema from Doctrine entities
- Optionally seeds test user data
- Interactive prompts for safety

### 2. Seed Users via Doctrine ORM
```bash
php bin/seed-users.php
```
- Uses Doctrine ORM to create User entities
- Creates 10 test users with realistic data
- Checks for existing data before seeding
- Safe to run multiple times

### 3. Load SQL Fixtures Directly
```bash
php bin/load-sql-fixtures.php
```
- Loads SQL fixtures directly into SQLite
- Faster than ORM approach
- Uses the `fixtures/users.sql` file
- Creates 20 test users

### 4. Doctrine Schema Management
```bash
# Create schema
php bin/doctrine.php orm:schema-tool:create

# Update schema
php bin/doctrine.php orm:schema-tool:update --force

# Drop schema
php bin/doctrine.php orm:schema-tool:drop --force
```

## Test Data Structure

### User Entity Fields
- `id` (integer, auto-increment)
- `username` (string, unique, max 48 chars)
- `first_name` (string, max 40 chars)
- `last_name` (string, max 40 chars)

### Sample Users Created
The scripts create users with realistic data including:
- john.doe (John Doe)
- jane.smith (Jane Smith)
- bob.wilson (Bob Wilson)
- alice.johnson (Alice Johnson)
- charlie.brown (Charlie Brown)
- And more...

## Usage in Tests

### Using UserFixtures Class
```php
use Tests\Fixtures\UserFixtures;
use Doctrine\ORM\EntityManager;

// In your test
$entityManager = $container->get(EntityManager::class);
$fixtures = new UserFixtures($entityManager);

// Create single user
$user = $fixtures->createUser(null, 'test.user', 'Test', 'User');

// Create multiple users
$users = $fixtures->createMultipleUsers(5);

// Load standard dataset
$users = $fixtures->loadStandardDataset();

// Clear all users
$fixtures->clearUsers();
```

### Direct Entity Creation
```php
use App\Domain\User\User;

$user = new User(null, 'username', 'FirstName', 'LastName');
$entityManager->persist($user);
$entityManager->flush();
```

## Database Location

The SQLite database is located at:
```
var/user.db
```

## Quick Start

1. **First time setup:**
   ```bash
   php bin/setup-database.php
   ```

2. **Just add test data to existing database:**
   ```bash
   php bin/seed-users.php
   ```

3. **Reset and reload from SQL:**
   ```bash
   php bin/load-sql-fixtures.php
   ```

## Testing the API

After seeding data, you can test the API endpoints:

```bash
# List all users
curl http://localhost:8080/users

# Get specific user
curl http://localhost:8080/user/1
```

## Files Overview

- `fixtures/users.sql` - Raw SQL insert statements
- `tests/Fixtures/UserFixtures.php` - PHP class for test fixtures
- `bin/seed-users.php` - Doctrine ORM seeder script
- `bin/setup-database.php` - Complete database setup
- `bin/load-sql-fixtures.php` - Direct SQL loader
- `fixtures/README.md` - This documentation

## Troubleshooting

### Database doesn't exist
```bash
php bin/doctrine.php orm:schema-tool:create
```

### Permission issues
```bash
chmod 664 var/user.db
chmod 775 var/
```

### Clear all data
```bash
rm var/user.db
php bin/setup-database.php
```
