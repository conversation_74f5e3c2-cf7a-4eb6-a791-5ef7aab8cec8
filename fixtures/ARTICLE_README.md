# Article Domain Documentation

This document provides comprehensive information about the Article domain implementation with separate `article.db` database.

## Overview

The Article domain is a complete implementation featuring:
- **Separate SQLite Database**: `var/article.db` (independent from user.db)
- **Rich Entity Model**: Articles with types, pricing, delivery, stock management
- **PHP 8.2 Enums**: Type-safe article categorization
- **Advanced Repository**: Complex queries, filtering, pagination
- **RESTful API**: Full CRUD operations with filtering
- **Test Fixtures**: Comprehensive test data management

## Database Schema

### Article Table Structure

```sql
CREATE TABLE article (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,  -- enum: blog_art, physical_art, digital_art, etc.
    sku VARCHAR(100) UNIQUE NOT NULL,
    price DECIMAL(10,2),
    currency VARCHAR(3) NOT NULL DEFAULT 'EUR',
    delivery_method VARCHAR(50),
    delivery_time_days INTEGER,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## Article Types (Enum)

### Available Types

| Type | Label | Description | Requires Delivery | Digital Content |
|------|-------|-------------|-------------------|-----------------|
| `blog_art` | Blog Article | Written articles and blog posts | ❌ | ✅ |
| `physical_art` | Physical Art | Physical artwork that can be shipped | ✅ | ❌ |
| `digital_art` | Digital Art | Digital artwork delivered electronically | ❌ | ✅ |
| `photography` | Photography | Photographic artwork and prints | ❌ | ❌ |
| `sculpture` | Sculpture | Three-dimensional sculptural works | ✅ | ❌ |
| `painting` | Painting | Painted artwork on various mediums | ✅ | ❌ |
| `drawing` | Drawing | Hand-drawn artwork and sketches | ✅ | ❌ |
| `mixed_media` | Mixed Media | Artwork combining multiple mediums | ✅ | ❌ |
| `collectible` | Collectible | Collectible art pieces and limited editions | ✅ | ❌ |
| `vintage` | Vintage | Vintage and antique art pieces | ✅ | ❌ |

### Enum Methods

```php
$type = ArticleType::DIGITAL_ART;

$type->getLabel();              // "Digital Art"
$type->getDescription();        // "Digital artwork delivered electronically"
$type->requiresPhysicalDelivery(); // false
$type->isDigitalContent();      // true

// Static methods
ArticleType::getAllTypes();     // All available types
ArticleType::getPhysicalTypes(); // Only physical delivery types
ArticleType::getDigitalTypes();  // Only digital content types
```

## API Endpoints

### List Articles
```bash
GET /articles

# Query Parameters:
# - type: Filter by article type (e.g., digital_art, painting)
# - in_stock: Filter by stock availability (1 for in stock)
# - search: Search in title, description, or SKU
# - page: Page number for pagination (default: 1)
# - limit: Items per page (default: 10)

# Examples:
curl "http://localhost:8080/articles"
curl "http://localhost:8080/articles?type=digital_art"
curl "http://localhost:8080/articles?in_stock=1"
curl "http://localhost:8080/articles?search=painting"
curl "http://localhost:8080/articles?page=2&limit=5"
```

### Get Article by ID
```bash
GET /article/{id}

curl "http://localhost:8080/article/1"
```

### Get Article by SKU
```bash
GET /article/sku/{sku}

curl "http://localhost:8080/article/sku/DIG-ABS-001"
```

## Database Setup Scripts

### Complete Setup
```bash
./bin/setup-article-database.php
```
- Creates article database schema
- Optionally seeds test data
- Interactive prompts for safety

### Seed Articles via Doctrine ORM
```bash
./bin/seed-articles.php
```
- Uses Doctrine ORM to create Article entities
- Creates 10 diverse test articles
- Checks for existing data before seeding

### Load SQL Fixtures
```bash
./bin/load-article-sql-fixtures.php
```
- Loads SQL fixtures directly into SQLite
- Creates 20 test articles with various types
- Faster than ORM approach

## Sample Data

### Test Articles Created

The seeders create articles covering all types:

1. **Digital Art**: Abstract masterpieces, tutorials
2. **Physical Art**: Oil paintings, sculptures, drawings
3. **Photography**: Urban life series, street photography
4. **Blog Content**: Art history articles, tutorials
5. **Collectibles**: Limited edition prints, vintage posters
6. **Mixed Media**: Collages, textile installations

### Example Article JSON Response

```json
{
    "id": 1,
    "title": "Abstract Digital Masterpiece",
    "description": "A stunning abstract digital artwork created with modern techniques.",
    "type": "digital_art",
    "typeLabel": "Digital Art",
    "sku": "DIG-ABS-001",
    "price": 299.99,
    "currency": "EUR",
    "formattedPrice": "299.99 EUR",
    "deliveryMethod": "Digital Download",
    "deliveryTimeDays": 1,
    "stockQuantity": 100,
    "isActive": true,
    "isInStock": true,
    "requiresDelivery": false,
    "createdAt": "2025-06-01 10:40:43",
    "updatedAt": "2025-06-01 10:40:43"
}
```

## Usage in Tests

### Using ArticleFixtures Class

```php
use Tests\Fixtures\ArticleFixtures;
use Doctrine\ORM\EntityManager;

// Get Article EntityManager
$entityManager = $container->get('ArticleEntityManager');
$fixtures = new ArticleFixtures($entityManager);

// Create single article
$article = $fixtures->createArticle(
    null, 
    'Test Article', 
    ArticleType::DIGITAL_ART, 
    'TEST-001'
);

// Create multiple articles
$articles = $fixtures->createMultipleArticles(5);

// Create articles by type
$digitalArts = $fixtures->createArticlesByType(ArticleType::DIGITAL_ART, 3);

// Load standard dataset
$articles = $fixtures->loadStandardDataset();

// Clear all articles
$fixtures->clearArticles();
```

## Repository Methods

### Basic Queries
```php
$repository = new ArticleRepository($entityManager);

// Find all active articles
$articles = $repository->findAllArticles();

// Find by ID
$article = $repository->findArticleOfId(1);

// Find by SKU
$article = $repository->findArticleBySku('DIG-ABS-001');
```

### Advanced Queries
```php
// Find by type
$digitalArts = $repository->findArticlesByType(ArticleType::DIGITAL_ART);

// Find in stock articles
$inStock = $repository->findInStockArticles();

// Find by price range
$affordable = $repository->findArticlesByPriceRange(50.0, 200.0);

// Search articles
$results = $repository->searchArticles('painting');

// Pagination
$page1 = $repository->findArticlesPaginated(1, 10);
```

## File Structure

```
src/Domain/Article/
├── Article.php              # Main entity
├── ArticleType.php          # Enum for article types
├── ArticleRepository.php    # Repository with advanced queries
└── ArticleNotFoundException.php

src/Application/Actions/Article/
├── ArticleAction.php        # Base action class
├── ListArticlesAction.php   # List with filtering
├── ViewArticleAction.php    # View by ID
└── ViewArticleBySkuAction.php # View by SKU

bin/
├── setup-article-database.php    # Complete setup
├── seed-articles.php             # ORM seeder
└── load-article-sql-fixtures.php # SQL loader

fixtures/
├── articles.sql           # SQL test data
└── ARTICLE_README.md     # This documentation

tests/Fixtures/
└── ArticleFixtures.php   # Test fixtures class
```

## Configuration

### Database Connection
The Article domain uses a separate EntityManager configured in `app/dependencies.php`:

```php
'ArticleEntityManager' => function (ContainerInterface $container): EntityManager {
    // Configured to use article.db database
    // Metadata only from src/Domain/Article
}
```

### Database Path
```php
// In app/settings.php
'connections' => [
    'default' => [
        'path' => __DIR__ . '/../var/user.db',    // Users
    ],
    'article' => [
        'path' => __DIR__ . '/../var/article.db', // Articles
    ],
]
```

## Troubleshooting

### Database doesn't exist
```bash
./bin/setup-article-database.php
```

### Clear all article data
```bash
rm var/article.db
./bin/setup-article-database.php
```

### Permission issues
```bash
chmod 664 var/article.db
chmod 775 var/
```

This implementation provides a complete, production-ready Article domain with comprehensive features for managing different types of artwork and content.
