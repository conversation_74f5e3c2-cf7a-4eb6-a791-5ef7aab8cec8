parameters:
	ignoreErrors:
		-
			message: "#^Method App\\\\Application\\\\Actions\\\\Action\\:\\:getFormData\\(\\) should return array\\|object but returns array\\|object\\|null\\.$#"
			count: 1
			path: src/Application/Actions/Action.php

		-
			message: "#^Parameter \\#1 \\$string of method Psr\\\\Http\\\\Message\\\\StreamInterface\\:\\:write\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/Application/Actions/Action.php

		-
			message: "#^Property App\\\\Application\\\\Actions\\\\Action\\:\\:\\$entityManager \\(Doctrine\\\\ORM\\\\EntityManager\\) does not accept mixed\\.$#"
			count: 1
			path: src/Application/Actions/Action.php

		-
			message: "#^Property App\\\\Application\\\\Actions\\\\Action\\:\\:\\$logger \\(Psr\\\\Log\\\\LoggerInterface\\) does not accept mixed\\.$#"
			count: 1
			path: src/Application/Actions/Action.php

		-
			message: "#^Method App\\\\Application\\\\Actions\\\\ActionPayload\\:\\:__construct\\(\\) has parameter \\$data with no type specified\\.$#"
			count: 1
			path: src/Application/Actions/ActionPayload.php

		-
			message: "#^Parameter \\#1 \\$entityManager of class App\\\\Domain\\\\Article\\\\ArticleRepository constructor expects Doctrine\\\\ORM\\\\EntityManagerInterface, mixed given\\.$#"
			count: 1
			path: src/Application/Actions/Article/ArticleAction.php

		-
			message: "#^Property App\\\\Application\\\\Actions\\\\Article\\\\ArticleAction\\:\\:\\$articleEntityManager \\(Doctrine\\\\ORM\\\\EntityManager\\) does not accept mixed\\.$#"
			count: 1
			path: src/Application/Actions/Article/ArticleAction.php

		-
			message: "#^Cannot cast mixed to int\\.$#"
			count: 1
			path: src/Application/Actions/Article/ViewArticleAction.php

		-
			message: "#^Cannot cast mixed to string\\.$#"
			count: 1
			path: src/Application/Actions/Article/ViewArticleBySkuAction.php

		-
			message: "#^Cannot cast mixed to int\\.$#"
			count: 1
			path: src/Application/Actions/User/ViewUserAction.php

		-
			message: "#^Parameter \\#1 \\$string of method Psr\\\\Http\\\\Message\\\\StreamInterface\\:\\:write\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/Application/Handlers/HttpErrorHandler.php

		-
			message: "#^Method App\\\\Application\\\\Handlers\\\\ShutdownHandler\\:\\:__invoke\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Application/Handlers/ShutdownHandler.php

		-
			message: "#^Property App\\\\Domain\\\\Article\\\\ArticleNotFoundException\\:\\:\\$message has no type specified\\.$#"
			count: 1
			path: src/Domain/Article/ArticleNotFoundException.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findAllArticles\\(\\) should return array\\<App\\\\Domain\\\\Article\\\\Article\\> but returns array\\<int, object\\>\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findAllArticlesIncludingInactive\\(\\) should return array\\<App\\\\Domain\\\\Article\\\\Article\\> but returns array\\<int, object\\>\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findArticleBySku\\(\\) should return App\\\\Domain\\\\Article\\\\Article\\|null but returns object\\|null\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findArticleOfId\\(\\) should return App\\\\Domain\\\\Article\\\\Article\\|null but returns object\\|null\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findArticlesBy\\(\\) should return array\\<App\\\\Domain\\\\Article\\\\Article\\> but returns array\\<int, object\\>\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findArticlesByPriceRange\\(\\) should return array\\<App\\\\Domain\\\\Article\\\\Article\\> but returns mixed\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findArticlesByType\\(\\) should return array\\<App\\\\Domain\\\\Article\\\\Article\\> but returns array\\<int, object\\>\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findArticlesPaginated\\(\\) should return array\\<App\\\\Domain\\\\Article\\\\Article\\> but returns mixed\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findInStockArticles\\(\\) should return array\\<App\\\\Domain\\\\Article\\\\Article\\> but returns mixed\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:findOneArticleBy\\(\\) should return App\\\\Domain\\\\Article\\\\Article\\|null but returns object\\|null\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Article\\\\ArticleRepository\\:\\:searchArticles\\(\\) should return array\\<App\\\\Domain\\\\Article\\\\Article\\> but returns mixed\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Parameter \\#3 \\$limit of method Doctrine\\\\ORM\\\\EntityRepository\\<object\\>\\:\\:findBy\\(\\) expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Parameter \\#4 \\$offset of method Doctrine\\\\ORM\\\\EntityRepository\\<object\\>\\:\\:findBy\\(\\) expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/Domain/Article/ArticleRepository.php

		-
			message: "#^Property App\\\\Domain\\\\User\\\\UserNotFoundException\\:\\:\\$message has no type specified\\.$#"
			count: 1
			path: src/Domain/User/UserNotFoundException.php

		-
			message: "#^Method App\\\\Domain\\\\User\\\\UserRepository\\:\\:findAllUsers\\(\\) should return array\\<App\\\\Domain\\\\User\\\\User\\> but returns array\\<int, object\\>\\.$#"
			count: 1
			path: src/Domain/User/UserRepository.php

		-
			message: "#^Method App\\\\Domain\\\\User\\\\UserRepository\\:\\:findOneUserBy\\(\\) should return App\\\\Domain\\\\User\\\\User\\|null but returns object\\|null\\.$#"
			count: 1
			path: src/Domain/User/UserRepository.php

		-
			message: "#^Method App\\\\Domain\\\\User\\\\UserRepository\\:\\:findUserOfId\\(\\) should return App\\\\Domain\\\\User\\\\User\\|null but returns object\\|null\\.$#"
			count: 1
			path: src/Domain/User/UserRepository.php

		-
			message: "#^Method App\\\\Domain\\\\User\\\\UserRepository\\:\\:findUsersBy\\(\\) should return array\\<App\\\\Domain\\\\User\\\\User\\> but returns array\\<int, object\\>\\.$#"
			count: 1
			path: src/Domain/User/UserRepository.php

		-
			message: "#^Parameter \\#3 \\$limit of method Doctrine\\\\ORM\\\\EntityRepository\\<object\\>\\:\\:findBy\\(\\) expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/Domain/User/UserRepository.php

		-
			message: "#^Parameter \\#4 \\$offset of method Doctrine\\\\ORM\\\\EntityRepository\\<object\\>\\:\\:findBy\\(\\) expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/Domain/User/UserRepository.php

		-
			message: "#^Method Tests\\\\Application\\\\Actions\\\\ActionTest\\:\\:testActionSetsHttpCodeInRespond\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Application/Actions/ActionTest.php

		-
			message: "#^Method Tests\\\\Application\\\\Actions\\\\ActionTest\\:\\:testActionSetsHttpCodeRespondData\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Application/Actions/ActionTest.php

		-
			message: "#^Parameter \\#1 \\$container of class class@anonymous/tests/Application/Actions/ActionTest\\.php\\:21 constructor expects Psr\\\\Container\\\\ContainerInterface, Psr\\\\Container\\\\ContainerInterface\\|null given\\.$#"
			count: 1
			path: tests/Application/Actions/ActionTest.php

		-
			message: "#^Parameter \\#1 \\$container of class class@anonymous/tests/Application/Actions/ActionTest\\.php\\:53 constructor expects Psr\\\\Container\\\\ContainerInterface, Psr\\\\Container\\\\ContainerInterface\\|null given\\.$#"
			count: 1
			path: tests/Application/Actions/ActionTest.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Tests\\\\Application\\\\Actions\\\\User\\\\Container\\.$#"
			count: 1
			path: tests/Application/Actions/User/ListUserActionTest.php

		-
			message: "#^Method Tests\\\\Application\\\\Actions\\\\User\\\\ListUserActionTest\\:\\:testAction\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Application/Actions/User/ListUserActionTest.php

		-
			message: "#^PHPDoc tag @var for variable \\$container contains unknown class Tests\\\\Application\\\\Actions\\\\User\\\\Container\\.$#"
			count: 1
			path: tests/Application/Actions/User/ListUserActionTest.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Tests\\\\Application\\\\Actions\\\\User\\\\Container\\.$#"
			count: 2
			path: tests/Application/Actions/User/ViewUserActionTest.php

		-
			message: "#^Method Tests\\\\Application\\\\Actions\\\\User\\\\ViewUserActionTest\\:\\:testAction\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Application/Actions/User/ViewUserActionTest.php

		-
			message: "#^Method Tests\\\\Application\\\\Actions\\\\User\\\\ViewUserActionTest\\:\\:testActionThrowsUserNotFoundException\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Application/Actions/User/ViewUserActionTest.php

		-
			message: "#^PHPDoc tag @var for variable \\$container contains unknown class Tests\\\\Application\\\\Actions\\\\User\\\\Container\\.$#"
			count: 2
			path: tests/Application/Actions/User/ViewUserActionTest.php

		-
			message: "#^Parameter \\#1 \\$status of class Slim\\\\Psr7\\\\Response constructor expects int, int\\|string given\\.$#"
			count: 1
			path: tests/Application/Actions/User/ViewUserActionTest.php

		-
			message: "#^Parameter \\#1 \\$statusCode of class App\\\\Application\\\\Actions\\\\ActionPayload constructor expects int, int\\|string given\\.$#"
			count: 1
			path: tests/Application/Actions/User/ViewUserActionTest.php

		-
			message: "#^Parameter \\#1 \\$string of method Psr\\\\Http\\\\Message\\\\StreamInterface\\:\\:write\\(\\) expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Application/Actions/User/ViewUserActionTest.php

		-
			message: "#^Cannot call method get\\(\\) on Psr\\\\Container\\\\ContainerInterface\\|null\\.$#"
			count: 1
			path: tests/Domain/Article/ArticleTest.php

		-
			message: "#^Method Tests\\\\Domain\\\\Article\\\\ArticleTest\\:\\:testArticleCreation\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Domain/Article/ArticleTest.php

		-
			message: "#^Method Tests\\\\Domain\\\\Article\\\\ArticleTest\\:\\:testArticleJsonSerialize\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Domain/Article/ArticleTest.php

		-
			message: "#^Method Tests\\\\Domain\\\\Article\\\\ArticleTest\\:\\:testArticleTypeEnum\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Domain/Article/ArticleTest.php

		-
			message: "#^Method Tests\\\\Domain\\\\Article\\\\ArticleTest\\:\\:testArticleWithNullPrice\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Domain/Article/ArticleTest.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Tests\\\\Domain\\\\User\\\\Container\\.$#"
			count: 1
			path: tests/Domain/User/UserTest.php

		-
			message: "#^Method Tests\\\\Domain\\\\User\\\\UserTest\\:\\:testJsonSerialize\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Domain/User/UserTest.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$firstName$#"
			count: 1
			path: tests/Domain/User/UserTest.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$id$#"
			count: 1
			path: tests/Domain/User/UserTest.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$lastName$#"
			count: 1
			path: tests/Domain/User/UserTest.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$username$#"
			count: 1
			path: tests/Domain/User/UserTest.php

		-
			message: "#^PHPDoc tag @var for variable \\$container contains unknown class Tests\\\\Domain\\\\User\\\\Container\\.$#"
			count: 1
			path: tests/Domain/User/UserTest.php

		-
			message: "#^Parameter \\#1 \\$resource of method Slim\\\\Psr7\\\\Factory\\\\StreamFactory\\:\\:createStreamFromResource\\(\\) expects resource, resource\\|false given\\.$#"
			count: 1
			path: tests/TestCase.php
