includes:
  - phpstan-baseline.neon

parameters:
  level: 6
  paths:
    - src
    - tests
  excludePaths:
    - tests/bootstrap.php
  ignoreErrors:
    # Ignore Doctrine proxy classes
    - '#Call to an undefined method Doctrine\\ORM\\EntityRepository::#'
    # Ignore container get() method
    - '#Cannot call method get\(\) on mixed#'
    # Ignore missing array typehints
    -
      identifier: missingType.iterableValue
    # Ignore missing generic typehints
    -
      identifier: missingType.generics
