# Telegram Error Logger

This document describes the implementation and usage of the Telegram Error Logger for real-time error monitoring in the Slim 4 application.

## Overview

The Telegram Error Logger is a Monolog handler that sends log messages directly to a Telegram channel or chat. This provides real-time notifications for errors, warnings, and other important events in your application.

### Features

- ✅ **Real-time notifications** - Instant error alerts via Telegram
- ✅ **Rich formatting** - Markdown formatting with emojis for different log levels
- ✅ **Context support** - Includes request context, user info, and custom data
- ✅ **Configurable levels** - Set minimum log level (Error, Warning, etc.)
- ✅ **Fallback logging** - Continues to log to files even if Telegram fails
- ✅ **Security** - No sensitive data exposure in messages

## Setup Instructions

### 1. Create a Telegram Bot

1. **Message @BotFather** on Telegram
2. **Send `/newbot` command**
3. **Follow the instructions** to create your bot
4. **Copy the bot token** (format: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### 2. Get Chat/Channel ID

**For Personal Chat:**
1. **Message your bot** with any text
2. **Visit this URL** (replace `<YOUR_BOT_TOKEN>` with your actual token):
   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
   ```
3. **Find the chat ID** in the response (usually a positive number)

**For Channel:**
1. **Add your bot** as an administrator to the channel
2. **Send a message** to the channel
3. **Use the same URL** as above to get the channel ID
4. **Channel ID** will be negative (e.g., `-1001234567890`)

### 3. Configure Environment Variables

Create or update your `.env` file:

```env
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHANNEL=-1001234567890
TELEGRAM_APP_NAME="My Slim4 App"
```

### 4. Test the Configuration

Visit the test endpoints to verify your setup:

```bash
# Test different log levels
curl "http://localhost:8080/test/telegram/error"
curl "http://localhost:8080/test/telegram/warning"
curl "http://localhost:8080/test/telegram/critical"
curl "http://localhost:8080/test/telegram/exception"
curl "http://localhost:8080/test/telegram/context"
```

## Configuration Options

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `TELEGRAM_BOT_TOKEN` | Bot token from @BotFather | Yes | - |
| `TELEGRAM_CHANNEL` | Chat/Channel ID | Yes | - |
| `TELEGRAM_APP_NAME` | App name in messages | No | "Slim4 App" |

### Log Levels

The Telegram handler is configured to log messages at **Error level and above** by default:

- 🐛 **Debug** - Not sent to Telegram (file only)
- ℹ️ **Info** - Not sent to Telegram (file only)
- 📢 **Notice** - Not sent to Telegram (file only)
- ⚠️ **Warning** - Sent to Telegram
- ❌ **Error** - Sent to Telegram
- 🚨 **Critical** - Sent to Telegram
- 🔥 **Alert** - Sent to Telegram
- 💥 **Emergency** - Sent to Telegram

## Usage Examples

### Basic Error Logging

```php
// In your action or service
$this->logger->error('Database connection failed', [
    'appName' => 'My App',
    'database' => 'user_db',
    'error_code' => 'DB_CONNECTION_ERROR',
]);
```

### Exception Logging

```php
try {
    // Some risky operation
    $result = $this->riskyOperation();
} catch (\Exception $e) {
    $this->logger->error('Operation failed', [
        'appName' => 'My App',
        'exception' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'user_id' => $userId ?? null,
    ]);
    
    throw $e; // Re-throw if needed
}
```

### Custom Context

```php
$this->logger->warning('High memory usage detected', [
    'appName' => 'My App',
    'memory_usage' => memory_get_usage(true),
    'memory_limit' => ini_get('memory_limit'),
    'request_uri' => $request->getUri()->getPath(),
    'user_ip' => $request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
]);
```

## Message Format

Telegram messages are formatted with Markdown and include:

```
🚨 *My App* - ERROR
🕐 2025-01-03 14:30:25

📝 *Message:*
Database connection failed

📋 *Context:*
• database: user_db
• error_code: DB_CONNECTION_ERROR
• user_ip: *************

🔧 *Extra:*
• memory_usage: 2048576
• request_id: abc123def456
```

## Security Considerations

### Data Privacy

- **No sensitive data** should be logged (passwords, tokens, personal data)
- **Limit context size** - Only essential debugging information
- **IP addresses** are included but can be disabled if needed

### Error Handling

- **Silent failures** - Telegram errors don't break your application
- **Fallback logging** - All messages still go to log files
- **Timeout protection** - 5-second timeout for Telegram API calls

## Troubleshooting

### Common Issues

**1. Messages not appearing in Telegram:**
- ✅ Check bot token is correct
- ✅ Verify chat/channel ID
- ✅ Ensure bot is added to channel (for channels)
- ✅ Check log files for Telegram API errors

**2. Bot token invalid:**
- ✅ Create new bot with @BotFather
- ✅ Copy token exactly (no extra spaces)
- ✅ Update `.env` file

**3. Permission denied:**
- ✅ Add bot as administrator to channel
- ✅ Give bot permission to send messages

### Debug Mode

Check application logs for Telegram handler errors:

```bash
tail -f var/logs/app.log | grep "TelegramHandler"
```

### Test Configuration

Use the built-in test endpoints:

```bash
# Test basic functionality
curl "http://localhost:8080/test/telegram/error"

# Check response
{
    "statusCode": 200,
    "data": {
        "message": "Telegram logger test 'error' executed",
        "timestamp": "2025-01-03 14:30:25",
        "note": "Check your Telegram channel for the log message"
    }
}
```

## Advanced Configuration

### Custom Log Levels

To change the minimum log level for Telegram:

```php
// In app/settings.php
'telegram' => [
    'token' => $_ENV['TELEGRAM_BOT_TOKEN'] ?? '',
    'channel' => $_ENV['TELEGRAM_CHANNEL'] ?? '',
    'app_name' => $_ENV['TELEGRAM_APP_NAME'] ?? 'Slim4 App',
    'level' => \Monolog\Level::Warning, // Send warnings and above
],
```

### Multiple Channels

For different log levels to different channels:

```php
// In dependencies.php - add multiple handlers
$errorHandler = new TelegramHandler(
    $telegramSettings['token'],
    $telegramSettings['error_channel'],
    $telegramSettings['app_name'],
    \Monolog\Level::Error
);

$warningHandler = new TelegramHandler(
    $telegramSettings['token'],
    $telegramSettings['warning_channel'],
    $telegramSettings['app_name'],
    \Monolog\Level::Warning
);

$logger->pushHandler($errorHandler);
$logger->pushHandler($warningHandler);
```

## Production Recommendations

1. **Use separate channels** for different environments (dev, staging, prod)
2. **Set appropriate log levels** (Error+ for production)
3. **Monitor message volume** to avoid spam
4. **Regular bot token rotation** for security
5. **Backup notification methods** (email, SMS) for critical systems

This Telegram Error Logger provides real-time visibility into your application's health and helps you respond quickly to issues.
