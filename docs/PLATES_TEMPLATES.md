# Plates Templates Implementation

This document describes the complete implementation of Plates template engine for rendering HTML views in the Slim 4 application.

## ✅ Implementation Status

**COMPLETED** - Plates templates have been fully implemented and are ready for production use.

### What was implemented:
- ✅ Plates template engine installation and configuration
- ✅ Template directory structure with organized folders
- ✅ Base TemplateAction class for template rendering
- ✅ Complete layout system with Bootstrap 5
- ✅ Reusable component system (header, footer, navigation)
- ✅ Homepage template with feature showcase
- ✅ Article templates (listing and card components)
- ✅ Error page templates (404 and generic error)
- ✅ Path integration with responsive-sk/slim4-paths
- ✅ Security features with automatic escaping
- ✅ Quality assurance (PSR-12, PHPStan level max, tests)

### Live Demo:
- **Homepage**: `http://localhost:8080/` - Bootstrap 5 responsive layout
- **Template inheritance**: Layout with sections and components
- **Error handling**: Custom 404 and error pages
- **Component reuse**: Header, footer, navigation components

## Overview

Plates is a native PHP template system that provides:
- ✅ **Native PHP syntax** - No new template language to learn
- ✅ **Template inheritance** - Layout and section support
- ✅ **Automatic escaping** - XSS protection built-in
- ✅ **Extensions** - Extensible with custom functions
- ✅ **Fast performance** - No compilation step needed

## Installation

```bash
composer require league/plates
```

## Configuration

### 1. Directory Structure
```
templates/
├── layouts/
│   ├── app.php          # Main application layout
│   └── admin.php        # Admin layout
├── pages/
│   ├── home.php         # Homepage template
│   ├── about.php        # About page template
│   └── contact.php      # Contact page template
├── components/
│   ├── header.php       # Header component
│   ├── footer.php       # Footer component
│   ├── navigation.php   # Navigation component
│   └── sidebar.php      # Sidebar component
├── articles/
│   ├── index.php        # Article listing
│   ├── show.php         # Single article view
│   └── card.php         # Article card component
├── users/
│   ├── index.php        # User listing
│   ├── profile.php      # User profile
│   └── login.php        # Login form
└── errors/
    ├── 404.php          # Not found page
    ├── 500.php          # Server error page
    └── error.php        # Generic error page
```

### 2. Plates Engine Setup

```php
// In dependencies.php
use League\Plates\Engine;

$container->set(Engine::class, function () {
    $templates = new Engine(getAppPaths()->templates());
    
    // Add template folders
    $templates->addFolder('layouts', getAppPaths()->templates('layouts'));
    $templates->addFolder('components', getAppPaths()->templates('components'));
    $templates->addFolder('articles', getAppPaths()->templates('articles'));
    $templates->addFolder('users', getAppPaths()->templates('users'));
    $templates->addFolder('errors', getAppPaths()->templates('errors'));
    
    // Register extensions
    $templates->loadExtension(new \League\Plates\Extension\Asset(
        getAppPaths()->public('assets')
    ));
    
    return $templates;
});
```

## Template Features

### 1. Layout Inheritance

**Main Layout (`templates/layouts/app.php`):**
```php
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->e($title ?? 'My App') ?></title>
    <link href="/assets/css/app.css" rel="stylesheet">
    <?= $this->section('head') ?>
</head>
<body>
    <?= $this->insert('components::header') ?>
    
    <main class="container">
        <?= $this->section('content') ?>
    </main>
    
    <?= $this->insert('components::footer') ?>
    
    <script src="/assets/js/app.js"></script>
    <?= $this->section('scripts') ?>
</body>
</html>
```

**Page Template (`templates/pages/home.php`):**
```php
<?php $this->layout('layouts::app', ['title' => 'Homepage']) ?>

<?php $this->start('content') ?>
<div class="hero">
    <h1>Welcome to <?= $this->e($appName) ?></h1>
    <p><?= $this->e($description) ?></p>
</div>

<div class="features">
    <?php foreach ($features as $feature): ?>
        <div class="feature-card">
            <h3><?= $this->e($feature['title']) ?></h3>
            <p><?= $this->e($feature['description']) ?></p>
        </div>
    <?php endforeach ?>
</div>
<?php $this->stop() ?>

<?php $this->start('scripts') ?>
<script>
    console.log('Homepage loaded');
</script>
<?php $this->stop() ?>
```

### 2. Component System

**Header Component (`templates/components/header.php`):**
```php
<header class="navbar">
    <div class="container">
        <a href="/" class="logo">
            <?= $this->e($appName ?? 'My App') ?>
        </a>
        
        <?= $this->insert('components::navigation') ?>
        
        <div class="user-menu">
            <?php if (isset($user)): ?>
                <span>Welcome, <?= $this->e($user['name']) ?></span>
                <a href="/logout">Logout</a>
            <?php else: ?>
                <a href="/login">Login</a>
            <?php endif ?>
        </div>
    </div>
</header>
```

**Navigation Component (`templates/components/navigation.php`):**
```php
<nav class="main-nav">
    <ul>
        <li><a href="/" class="<?= $currentPage === 'home' ? 'active' : '' ?>">Home</a></li>
        <li><a href="/articles" class="<?= $currentPage === 'articles' ? 'active' : '' ?>">Articles</a></li>
        <li><a href="/users" class="<?= $currentPage === 'users' ? 'active' : '' ?>">Users</a></li>
        <li><a href="/about" class="<?= $currentPage === 'about' ? 'active' : '' ?>">About</a></li>
    </ul>
</nav>
```

### 3. Data Binding and Security

**Article Listing (`templates/articles/index.php`):**
```php
<?php $this->layout('layouts::app', ['title' => 'Articles']) ?>

<?php $this->start('content') ?>
<div class="articles-page">
    <div class="page-header">
        <h1>Articles</h1>
        <a href="/articles/create" class="btn btn-primary">Create Article</a>
    </div>
    
    <?php if (empty($articles)): ?>
        <div class="empty-state">
            <p>No articles found.</p>
            <a href="/articles/create" class="btn">Create your first article</a>
        </div>
    <?php else: ?>
        <div class="articles-grid">
            <?php foreach ($articles as $article): ?>
                <?= $this->insert('articles::card', ['article' => $article]) ?>
            <?php endforeach ?>
        </div>
        
        <?php if (isset($pagination)): ?>
            <?= $this->insert('components::pagination', ['pagination' => $pagination]) ?>
        <?php endif ?>
    <?php endif ?>
</div>
<?php $this->stop() ?>
```

**Article Card Component (`templates/articles/card.php`):**
```php
<article class="article-card">
    <div class="article-image">
        <?php if ($article['image']): ?>
            <img src="<?= $this->e($article['image']) ?>" alt="<?= $this->e($article['title']) ?>">
        <?php else: ?>
            <div class="placeholder-image"></div>
        <?php endif ?>
    </div>
    
    <div class="article-content">
        <h3>
            <a href="/articles/<?= $this->e($article['id']) ?>">
                <?= $this->e($article['title']) ?>
            </a>
        </h3>
        
        <p class="article-excerpt">
            <?= $this->e(substr($article['description'], 0, 150)) ?>...
        </p>
        
        <div class="article-meta">
            <span class="article-type"><?= $this->e($article['typeLabel']) ?></span>
            <?php if ($article['price']): ?>
                <span class="article-price"><?= $this->e($article['formattedPrice']) ?></span>
            <?php endif ?>
            <time datetime="<?= $article['createdAt'] ?>">
                <?= date('M j, Y', strtotime($article['createdAt'])) ?>
            </time>
        </div>
    </div>
</article>
```

## Action Integration

### 1. Template Rendering Action

```php
<?php

namespace App\Application\Actions;

use League\Plates\Engine;
use Psr\Http\Message\ResponseInterface as Response;

abstract class TemplateAction extends Action
{
    protected Engine $templates;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        $this->templates = $container->get(Engine::class);
    }

    /**
     * Render template with data
     */
    protected function render(string $template, array $data = []): Response
    {
        // Add global template variables
        $data = array_merge($this->getGlobalData(), $data);
        
        $html = $this->templates->render($template, $data);
        
        $this->response->getBody()->write($html);
        return $this->response->withHeader('Content-Type', 'text/html');
    }

    /**
     * Get global template data
     */
    protected function getGlobalData(): array
    {
        return [
            'appName' => 'My Slim App',
            'currentPage' => $this->getCurrentPage(),
            'user' => $this->getCurrentUser(),
            'csrfToken' => $this->generateCsrfToken(),
        ];
    }
}
```

### 2. Page Actions

```php
<?php

namespace App\Application\Actions\Pages;

use App\Application\Actions\TemplateAction;
use Psr\Http\Message\ResponseInterface as Response;

class HomeAction extends TemplateAction
{
    protected function action(): Response
    {
        $data = [
            'title' => 'Welcome Home',
            'description' => 'This is the homepage of our application',
            'features' => [
                ['title' => 'Fast', 'description' => 'Built with Slim 4'],
                ['title' => 'Secure', 'description' => 'Path security included'],
                ['title' => 'Modern', 'description' => 'PHP 8.2+ features'],
            ]
        ];

        return $this->render('pages::home', $data);
    }
}
```

## Error Templates

### 1. 404 Error Template (`templates/errors/404.php`)

```php
<?php $this->layout('layouts::app', ['title' => 'Page Not Found']) ?>

<?php $this->start('content') ?>
<div class="error-page">
    <div class="error-content">
        <h1 class="error-code">404</h1>
        <h2>Page Not Found</h2>
        <p>The page you're looking for doesn't exist.</p>
        
        <div class="error-actions">
            <a href="/" class="btn btn-primary">Go Home</a>
            <a href="/articles" class="btn btn-secondary">Browse Articles</a>
        </div>
    </div>
</div>
<?php $this->stop() ?>
```

## Best Practices

### 1. Security
- ✅ **Always escape output** using `$this->e()`
- ✅ **Validate input data** before passing to templates
- ✅ **Use CSRF tokens** for forms
- ✅ **Sanitize user content** before display

### 2. Performance
- ✅ **Cache compiled templates** in production
- ✅ **Minimize template includes** for better performance
- ✅ **Use template inheritance** instead of includes where possible
- ✅ **Optimize asset loading** with proper caching headers

### 3. Maintainability
- ✅ **Organize templates** by feature/domain
- ✅ **Create reusable components** for common UI elements
- ✅ **Use consistent naming** conventions
- ✅ **Document template variables** and their types

## Testing Templates

### 1. Template Unit Tests

```php
<?php

namespace Tests\Templates;

use League\Plates\Engine;
use Tests\TestCase;

class TemplateTest extends TestCase
{
    private Engine $templates;

    protected function setUp(): void
    {
        parent::setUp();
        $this->templates = $this->getAppInstance()
            ->getContainer()
            ->get(Engine::class);
    }

    public function testHomePageRender(): void
    {
        $data = [
            'title' => 'Test Title',
            'features' => [
                ['title' => 'Feature 1', 'description' => 'Description 1']
            ]
        ];

        $html = $this->templates->render('pages::home', $data);

        $this->assertStringContainsString('Test Title', $html);
        $this->assertStringContainsString('Feature 1', $html);
    }
}
```

This implementation provides a complete, secure, and maintainable template system using Plates for the Slim 4 application.

## 📋 Preparation of Plates Templates

This section documents the step-by-step preparation and implementation of Plates templates in the Slim 4 application.

### Step 1: Installation and Dependencies

```bash
# Install Plates template engine
composer require league/plates
```

### Step 2: Directory Structure Creation

```bash
# Create template directory structure
mkdir -p templates/{layouts,pages,components,articles,users,errors}
```

**Created directories:**
- `templates/layouts/` - Main application layouts
- `templates/pages/` - Individual page templates
- `templates/components/` - Reusable UI components
- `templates/articles/` - Article-specific templates
- `templates/users/` - User-specific templates
- `templates/errors/` - Error page templates

### Step 3: DI Container Configuration

**File: `app/dependencies.php`**
```php
use League\Plates\Engine;

// Plates Template Engine
Engine::class => function (ContainerInterface $container): Engine {
    $paths = $container->get(Paths::class);
    $templates = new Engine($paths->templates());

    // Add template folders
    $templates->addFolder('layouts', $paths->templates('layouts'));
    $templates->addFolder('components', $paths->templates('components'));
    $templates->addFolder('articles', $paths->templates('articles'));
    $templates->addFolder('users', $paths->templates('users'));
    $templates->addFolder('errors', $paths->templates('errors'));
    $templates->addFolder('pages', $paths->templates('pages'));

    return $templates;
},
```

### Step 4: Base TemplateAction Class

**File: `src/Application/Actions/TemplateAction.php`**

Key features implemented:
- Template rendering with global data
- Error page rendering with status codes
- JSON response support for AJAX requests
- Flash message system
- Request type detection (HTML vs JSON)

### Step 5: Layout System Implementation

**Main Layout (`templates/layouts/app.php`):**
- Bootstrap 5 integration
- Responsive design
- Section support (head, main, scripts)
- Component integration
- Global variables access

### Step 6: Component System

**Created components:**
- **Header** (`templates/components/header.php`) - Navigation bar
- **Footer** (`templates/components/footer.php`) - Site footer
- **Navigation** (`templates/components/navigation.php`) - Main menu

### Step 7: Page Templates

**Homepage** (`templates/pages/home.php`):**
- Feature showcase
- API demo links
- Quality metrics display
- Interactive elements

### Step 8: Error Templates

**Error pages:**
- **404 Page** (`templates/errors/404.php`) - User-friendly not found
- **Generic Error** (`templates/errors/error.php`) - Customizable error page

### Step 9: Article Templates

**Article system:**
- **Listing** (`templates/articles/index.php`) - Article grid layout
- **Card Component** (`templates/articles/card.php`) - Reusable article card

### Step 10: Route Integration

**File: `app/routes.php`**
```php
use App\Application\Actions\Pages\HomeAction;

// Homepage with Plates template
$app->get('/', HomeAction::class)->setName('home');
```

### Step 11: Action Implementation

**File: `src/Application/Actions/Pages/HomeAction.php`**
```php
class HomeAction extends TemplateAction
{
    protected function action(): Response
    {
        $data = [
            'title' => 'Welcome to Slim 4 App',
            'description' => 'Modern PHP application...',
            'features' => $this->getFeatures(),
        ];

        return $this->render('pages::home', $data);
    }
}
```

### Step 12: Security Implementation

**XSS Protection:**
- Automatic escaping with `$this->e()`
- Input validation in actions
- Safe data binding practices

**Path Security Integration:**
- Templates stored in secure paths
- Path traversal protection
- Realpath validation

### Step 13: Quality Assurance

**Code Quality:**
- PSR-12 compliance
- PHPStan level max analysis
- Unit test coverage
- Automated quality checks

**Testing:**
```bash
# Run quality checks
./bin/quality-check.sh

# Results:
# ✅ Coding Standards: PASSED
# ✅ Static Analysis: PASSED
# ✅ Tests: PASSED (32 tests, 104 assertions)
```

### Step 14: Documentation

**Created documentation:**
- Complete implementation guide
- Usage examples
- Best practices
- Security considerations
- Testing instructions

### 🎯 Final Result

**Live Application:**
- **URL**: `http://localhost:8080/`
- **Features**: Bootstrap 5 responsive layout
- **Components**: Header, footer, navigation
- **Templates**: Homepage, articles, errors
- **Security**: XSS protection, path security
- **Quality**: Production-ready code

### 🔧 Maintenance

**Template Updates:**
1. Edit templates in `templates/` directory
2. No compilation step needed (native PHP)
3. Changes visible immediately
4. Run quality checks before deployment

**Adding New Templates:**
1. Create template file in appropriate folder
2. Extend TemplateAction for new actions
3. Add routes in `app/routes.php`
4. Test with quality checks

**Component Development:**
1. Create component in `templates/components/`
2. Use `$this->insert('components::name')` to include
3. Pass data as second parameter
4. Maintain reusability principles

This preparation guide ensures consistent, secure, and maintainable template implementation across the entire application.
