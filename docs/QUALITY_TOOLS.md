# Code Quality Tools Documentation

This document describes the code quality tools and scripts available in this project.

## Overview

The project includes comprehensive code quality tools:
- **PHPStan** - Static analysis (level: max)
- **PHP CodeSniffer** - Coding standards (PSR-12 + custom rules)
- **PHP Code Beautifier** - Automatic code fixing
- **Quality Check Scripts** - Automated quality workflows

## Available Scripts

### Composer Scripts

```bash
# Basic commands
composer phpstan          # Run PHPStan static analysis
composer cs-check          # Check coding standards
composer cs-fix            # Fix coding standards automatically

# Extended commands
composer phpstan-baseline   # Generate PHPStan baseline
composer cs-check-diff      # Show diff of what needs fixing
composer quality           # Run all quality checks
composer quality-fix       # Fix issues and run checks
```

### Bash Scripts (Enhanced)

#### 1. PHPStan Static Analysis
```bash
./bin/phpstan.sh                    # Standard analysis
./bin/phpstan.sh --level=8          # Custom analysis level
./bin/phpstan.sh --baseline         # Generate baseline
./bin/phpstan.sh --clear-cache      # Clear cache and run
./bin/phpstan.sh --memory=2G        # Use more memory
./bin/phpstan.sh --help             # Show help
```

#### 2. Coding Standards Check
```bash
./bin/cs-check.sh                   # Standard check
./bin/cs-check.sh --diff            # Show diff format
./bin/cs-check.sh --summary         # Show summary report
./bin/cs-check.sh --json            # Show JSON format
./bin/cs-check.sh --help            # Show help
```

#### 3. Coding Standards Fix
```bash
./bin/cs-fix.sh                     # Fix all issues
./bin/cs-fix.sh --dry-run           # Show what would be fixed
./bin/cs-fix.sh --help              # Show help
```

#### 4. Complete Quality Check
```bash
./bin/quality-check.sh              # Full quality check
./bin/quality-check.sh --fix        # Fix CS issues first
./bin/quality-check.sh --fast       # Skip tests (CS + PHPStan only)
./bin/quality-check.sh --coverage   # Include test coverage
./bin/quality-check.sh --help       # Show help
```

## Configuration Files

### PHPStan Configuration (`phpstan.neon.dist`)
```yaml
includes:
  - phpstan-baseline.neon

parameters:
  level: max                    # Highest analysis level
  paths:
    - src                       # Analyze source code
    - tests                     # Analyze tests
  excludePaths:
    - tests/bootstrap.php       # Exclude bootstrap
  ignoreErrors:
    # Ignore missing array typehints
    -
      identifier: missingType.iterableValue
    # Ignore missing generic typehints
    -
      identifier: missingType.generics
```

### PHP CodeSniffer Configuration (`phpcs.xml`)
```xml
<rule ref="PSR12"/>                           <!-- PSR-12 standard -->
<rule ref="Generic.Arrays.DisallowLongArraySyntax"/>  <!-- No array() syntax -->
<rule ref="Generic.PHP.ForbiddenFunctions">   <!-- Forbidden functions -->
  <properties>
    <property name="forbiddenFunctions" type="array">
      <element key="var_dump" value="null"/>
      <element key="print_r" value="null"/>
      <element key="die" value="exit"/>
    </property>
  </properties>
</rule>
```

## Quality Standards

### Coding Standards (PSR-12)
- ✅ PSR-12 compliance
- ✅ No long array syntax (`array()` → `[]`)
- ✅ No debugging functions (`var_dump`, `print_r`)
- ✅ Proper indentation and spacing
- ✅ Consistent naming conventions

### Static Analysis (PHPStan Level Max)
- ✅ Type safety
- ✅ Null safety
- ✅ Dead code detection
- ✅ Unreachable code detection
- ✅ Method signature validation
- ✅ Property type validation

## Workflow Integration

### Pre-commit Hook (Recommended)
```bash
#!/bin/sh
# .git/hooks/pre-commit
./bin/quality-check.sh --fast
```

### CI/CD Pipeline
```yaml
# Example GitHub Actions
- name: Quality Check
  run: |
    composer install
    ./bin/quality-check.sh
```

### Development Workflow
1. **During Development**: Use `./bin/cs-fix.sh` to fix formatting
2. **Before Commit**: Run `./bin/quality-check.sh --fast`
3. **Before Push**: Run `./bin/quality-check.sh` (with tests)

## Baseline Management

### PHPStan Baseline
The project uses a baseline to ignore existing issues while preventing new ones:

```bash
# Generate new baseline (when fixing issues)
./bin/phpstan.sh --baseline

# View current baseline
cat phpstan-baseline.neon
```

### Baseline Strategy
1. **Initial Setup**: Generate baseline for existing codebase
2. **Gradual Improvement**: Fix issues and regenerate baseline
3. **New Code**: Must pass without baseline exceptions

## Troubleshooting

### Common Issues

#### PHPStan Memory Issues
```bash
./bin/phpstan.sh --memory=2G
```

#### CS-Fix Permission Issues
```bash
chmod +x bin/*.sh
```

#### Xdebug Warnings
Add to `php.ini`:
```ini
xdebug.mode=off
```

### Performance Tips
- Use `--fast` mode for quick checks
- Clear PHPStan cache if issues persist
- Run CS-fix before CS-check to reduce violations

## Examples

### Daily Development
```bash
# Quick check before commit
./bin/quality-check.sh --fast

# Fix and check
./bin/quality-check.sh --fix
```

### Code Review Preparation
```bash
# Full quality check with coverage
./bin/quality-check.sh --coverage
```

### Debugging Quality Issues
```bash
# See what needs fixing
./bin/cs-check.sh --diff

# Dry run to see what would be fixed
./bin/cs-fix.sh --dry-run

# Clear PHPStan cache
./bin/phpstan.sh --clear-cache
```

## Integration with IDEs

### VS Code
Install extensions:
- `bmewburn.vscode-intelephense-client`
- `ValeryanM.vscode-phpsab`

### PhpStorm
- Enable PHPStan inspection
- Configure PHP CS Fixer
- Set up quality tools in External Tools

## Quality Metrics

The project maintains:
- **100% PSR-12 compliance**
- **PHPStan level max** with baseline
- **Automated fixing** for most issues
- **Comprehensive test coverage**

This ensures high code quality and maintainability across the entire codebase.
