# Path Security Implementation

This document describes the comprehensive path security implementation using `responsive-sk/slim4-paths` package to prevent path traversal attacks and unauthorized file access.

## Overview

The path security system protects against:
- **Path traversal attacks** (`../`, `..\\`, URL encoded variants)
- **Unauthorized file access** outside allowed directories
- **Directory enumeration** attacks
- **File inclusion vulnerabilities**

## Components

### 1. PathSecurityMiddleware

Global middleware that validates all incoming requests for path traversal attempts.

**Features:**
- ✅ Validates query parameters, POST data, and route arguments
- ✅ Blocks common path traversal patterns (`../`, `..%2f`, etc.)
- ✅ Uses `realpath()` for secure path resolution
- ✅ Restricts access to predefined allowed directories
- ✅ Logs security violation attempts

**Usage:**
```php
// Automatically applied to all routes via middleware.php
// Blocks requests like: ?file=../../../etc/passwd
```

### 2. SecurePathHelper

Helper class for safe file operations within allowed directories.

**Features:**
- ✅ Secure path resolution with directory restrictions
- ✅ Safe file reading/writing operations
- ✅ Directory listing with security validation
- ✅ Public URL generation for safe files
- ✅ Built-in path traversal protection

**Usage:**
```php
$helper = $container->get(SecurePathHelper::class);

// Safe file access
$content = $helper->readFile('demo/file.txt', 'public');

// Safe file writing
$helper->writeFile('output.txt', 'content', 'storage');

// Safe directory listing
$files = $helper->listFiles('uploads', 'public');
```

### 3. Paths Integration

Uses `responsive-sk/slim4-paths` for standardized directory management.

**Allowed Directories:**
- `public/` - Public web files
- `public/uploads/` - User uploads
- `public/assets/` - Static assets
- `templates/` - Template files
- `var/storage/` - Application storage
- `var/cache/` - Cache files
- `var/logs/` - Log files
- `config/` - Configuration files

## Security Features

### Path Traversal Protection

**Blocked Patterns:**
```
../          # Unix path traversal
..\\         # Windows path traversal
..%2f        # URL encoded Unix
..%2F        # URL encoded Unix (uppercase)
..%5c        # URL encoded Windows
..%5C        # URL encoded Windows (uppercase)
%2e%2e%2f    # Double URL encoded Unix
%2e%2e%5c    # Double URL encoded Windows
....//       # Double dot bypass attempt
....\\       # Double dot bypass attempt (Windows)
.%2e/        # Mixed encoding
.%2e\\       # Mixed encoding (Windows)
```

### Directory Restrictions

All file operations are restricted to predefined allowed directories:

```php
// ✅ ALLOWED - within public directory
/security/file?file=demo/safe-file.txt&dir=public

// ❌ BLOCKED - path traversal attempt
/security/file?file=../../../etc/passwd&dir=public

// ❌ BLOCKED - outside allowed directories
/security/file?file=/etc/passwd&dir=public
```

### Real Path Validation

Uses PHP's `realpath()` function to:
- Resolve symbolic links
- Normalize path separators
- Eliminate `.` and `..` components
- Validate final path is within allowed directories

## API Endpoints

### File Access Demo

```bash
# Safe file access
GET /security/file?file=demo/safe-file.txt&dir=public

# Response:
{
  "statusCode": 200,
  "data": {
    "requested_path": "demo/safe-file.txt",
    "directory": "public",
    "secure_path": "/path/to/public/demo/safe-file.txt",
    "exists": true,
    "readable": true,
    "size": 308,
    "modified": "2025-06-01 11:33:45",
    "mime_type": "text/plain",
    "content_preview": "File content..."
  }
}
```

### Directory Listing Demo

```bash
# Safe directory listing
GET /security/list?path=demo&base=public

# Response:
{
  "statusCode": 200,
  "data": {
    "directory": "demo",
    "base_directory": "public",
    "file_count": 1,
    "files": [
      {
        "name": "safe-file.txt",
        "type": "file",
        "size": 308,
        "modified": 1748777625
      }
    ],
    "allowed_directories": ["public", "uploads", "assets", ...]
  }
}
```

### Security Violation Response

```bash
# Path traversal attempt
GET /security/file?file=../../../etc/passwd&dir=public

# Response:
{
  "statusCode": 400,
  "error": {
    "type": "BAD_REQUEST",
    "description": "Path traversal attempt detected in parameter 'file': ../"
  }
}
```

## Testing

### Unit Tests

**PathSecurityMiddlewareTest:**
- ✅ Allows normal requests
- ✅ Blocks path traversal in query params
- ✅ Blocks URL encoded path traversal
- ✅ Blocks Windows path traversal
- ✅ Blocks path traversal in POST data
- ✅ Blocks nested path traversal
- ✅ Supports custom allowed directories
- ✅ Supports custom blocked patterns

**SecurePathHelperTest:**
- ✅ Secure path resolution
- ✅ Path traversal blocking
- ✅ File existence checking
- ✅ Safe file reading/writing
- ✅ Directory listing
- ✅ Invalid directory handling

### Manual Testing

```bash
# Test safe access
curl "http://localhost:8080/security/file?file=demo/safe-file.txt&dir=public"

# Test path traversal (should be blocked)
curl "http://localhost:8080/security/file?file=../../../etc/passwd&dir=public"

# Test URL encoded attack (should be blocked)
curl "http://localhost:8080/security/file?file=..%2f..%2f..%2fetc%2fpasswd&dir=public"
```

## Configuration

### Adding Custom Allowed Directories

```php
// In dependencies.php or middleware configuration
$pathSecurity = $container->get(PathSecurityMiddleware::class);
$pathSecurity->addAllowedDirectory('/custom/safe/directory');

$secureHelper = $container->get(SecurePathHelper::class);
$secureHelper->addAllowedDirectory('custom', '/custom/safe/directory');
```

### Adding Custom Blocked Patterns

```php
$pathSecurity = $container->get(PathSecurityMiddleware::class);
$pathSecurity->addBlockedPattern('forbidden-pattern');
```

## Logging

Security violations are automatically logged:

```php
// Example log entry
[2025-06-01 11:45:23] app.WARNING: Path security violation attempt {
  "file": "../../../etc/passwd",
  "directory": "public", 
  "error": "Path traversal attempt detected: ../",
  "ip": "*************"
}
```

## Best Practices

### 1. Always Use SecurePathHelper

```php
// ❌ DANGEROUS - Direct file access
$content = file_get_contents($_GET['file']);

// ✅ SAFE - Using SecurePathHelper
$content = $secureHelper->readFile($_GET['file'], 'public');
```

### 2. Validate File Types

```php
// Add file type validation
$allowedExtensions = ['txt', 'jpg', 'png', 'pdf'];
$extension = pathinfo($filename, PATHINFO_EXTENSION);
if (!in_array(strtolower($extension), $allowedExtensions)) {
    throw new InvalidArgumentException('File type not allowed');
}
```

### 3. Limit File Sizes

```php
// Check file size before operations
if (filesize($securePath) > 10 * 1024 * 1024) { // 10MB limit
    throw new InvalidArgumentException('File too large');
}
```

### 4. Use Appropriate Base Directories

```php
// User uploads
$secureHelper->writeFile($filename, $content, 'uploads');

// Application data
$secureHelper->writeFile($filename, $content, 'storage');

// Public assets
$secureHelper->readFile($filename, 'public');
```

## Security Considerations

### 1. Defense in Depth
- Middleware blocks attacks at request level
- SecurePathHelper provides additional validation
- Real path resolution prevents bypass attempts

### 2. Logging and Monitoring
- All security violations are logged
- Monitor logs for attack patterns
- Set up alerts for repeated violations

### 3. Regular Updates
- Keep `responsive-sk/slim4-paths` updated
- Review and update blocked patterns
- Test security with new attack vectors

This implementation provides comprehensive protection against path traversal attacks while maintaining usability for legitimate file operations.
