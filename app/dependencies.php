<?php

/**
 * Set dependencies of the application.
 *
 * @param ContainerBuilder $containerBuilder The container builder
 *
 * @return void
 */

declare(strict_types=1);

use App\Application\Settings\SettingsInterface;
use App\Application\Helpers\SecurePathHelper;
use App\Application\Middleware\PathSecurityMiddleware;
use App\Application\Monolog\Handler\TelegramHandler;
use DI\ContainerBuilder;
use League\Plates\Engine;
use Doctrine\DBAL\DriverManager;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\ORMSetup;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Monolog\Processor\UidProcessor;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use ResponsiveSk\Slim4Paths\Paths;
use Symfony\Component\Cache\Adapter\ArrayAdapter;
use Symfony\Component\Cache\Adapter\PhpFilesAdapter;

return function (
    ContainerBuilder $containerBuilder
) {
    $containerBuilder->addDefinitions([
        // Logger
        LoggerInterface::class => function (ContainerInterface $c): LoggerInterface {
            $settings = $c->get(SettingsInterface::class);

            // Get Logger settings
            $loggerSettings = $settings->get('logger');

            // Initialize the Logger
            $logger = new Logger($loggerSettings['name']);

            // Push Uid processor
            $processor = new UidProcessor();
            $logger->pushProcessor($processor);

            // Push stream handler
            $handler = new StreamHandler($loggerSettings['path'], $loggerSettings['level']);
            $logger->pushHandler($handler);

            // Add Telegram handler if configured
            $telegramSettings = $settings->get('telegram');
            if (!empty($telegramSettings['token']) && !empty($telegramSettings['channel'])) {
                $telegramHandler = new TelegramHandler(
                    $telegramSettings['token'],
                    $telegramSettings['channel'],
                    $telegramSettings['app_name'] ?? 'Slim4 App',
                    $telegramSettings['level'] ?? \Monolog\Level::Error
                );
                $logger->pushHandler($telegramHandler);
            }

            // Return the logger
            return $logger;
        },

        // Doctrine Entity Manager
        EntityManager::class => function (ContainerInterface $container): EntityManager {
            /** @var SettingsInterface $settings */
            $settings = $container->get(SettingsInterface::class);

            /** @var array $doctrineSettings */
            $doctrineSettings = $settings->get('doctrine');

            // Create the configuration for annotation metadata
            $config = ORMSetup::createAttributeMetadataConfiguration(
                $doctrineSettings['metadata_dirs'],
                $doctrineSettings['dev_mode'],
                $doctrineSettings['proxy_dir'],
                null,
                false
            );

            // Set cache
            if ($doctrineSettings['dev_mode']) {
                $metadataCache = new ArrayAdapter();
                $queryCache = new ArrayAdapter();
                $resultCache = new ArrayAdapter();
            } else {
                $metadataCache = new PhpFilesAdapter('doctrine_metadata');
                $queryCache = new PhpFilesAdapter('doctrine_queries');
                $resultCache = new PhpFilesAdapter('doctrine_cache');
            }

            // Set metadata cache
            $config->setMetadataCache(
                $metadataCache
            );

            // Set query cache
            $config->setQueryCache(
                $queryCache
            );

            // Set result cache
            $config->setResultCache(
                $resultCache
            );

            // configuring the database connection
            $connection = DriverManager::getConnection(
                $doctrineSettings['connections']['default'],
                $config
            );

            // obtaining the entity manager
            $entityManager = new EntityManager($connection, $config);

            // Return the new entity manager created
            return $entityManager;
        },

        // Article Entity Manager (separate database)
        'ArticleEntityManager' => function (ContainerInterface $container): EntityManager {
            /** @var SettingsInterface $settings */
            $settings = $container->get(SettingsInterface::class);

            /** @var array $doctrineSettings */
            $doctrineSettings = $settings->get('doctrine');

            // Create the configuration for annotation metadata (only Article domain)
            $config = ORMSetup::createAttributeMetadataConfiguration(
                [getAppPaths()->path('src/Domain/Article')],
                $doctrineSettings['dev_mode'],
                $doctrineSettings['proxy_dir'],
                null,
                false
            );

            // Set cache
            if ($doctrineSettings['dev_mode']) {
                $metadataCache = new ArrayAdapter();
                $queryCache = new ArrayAdapter();
                $resultCache = new ArrayAdapter();
            } else {
                $metadataCache = new PhpFilesAdapter('doctrine_metadata_article');
                $queryCache = new PhpFilesAdapter('doctrine_queries_article');
                $resultCache = new PhpFilesAdapter('doctrine_cache_article');
            }

            // Set metadata cache
            $config->setMetadataCache($metadataCache);
            $config->setQueryCache($queryCache);
            $config->setResultCache($resultCache);

            // configuring the database connection for article database
            $connection = DriverManager::getConnection(
                $doctrineSettings['connections']['article'],
                $config
            );

            // obtaining the entity manager
            $entityManager = new EntityManager($connection, $config);

            // Return the new entity manager created
            return $entityManager;
        },

        // Paths Management
        Paths::class => function (): Paths {
            return getAppPaths();
        },

        // Secure Path Helper
        SecurePathHelper::class => function (ContainerInterface $container): SecurePathHelper {
            $paths = $container->get(Paths::class);
            return new SecurePathHelper($paths);
        },

        // Path Security Middleware
        PathSecurityMiddleware::class => function (ContainerInterface $container): PathSecurityMiddleware {
            $paths = $container->get(Paths::class);
            return new PathSecurityMiddleware($paths);
        },

        // Plates Template Engine
        Engine::class => function (ContainerInterface $container): Engine {
            $paths = $container->get(Paths::class);
            $templates = new Engine($paths->templates());

            // Add template folders
            $templates->addFolder('layouts', $paths->templates('layouts'));
            $templates->addFolder('components', $paths->templates('components'));
            $templates->addFolder('articles', $paths->templates('articles'));
            $templates->addFolder('users', $paths->templates('users'));
            $templates->addFolder('errors', $paths->templates('errors'));
            $templates->addFolder('pages', $paths->templates('pages'));

            return $templates;
        },
    ]);
};
