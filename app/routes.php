<?php

/**
 * Application Routes
 *
 * This file loads and registers all application routes.
 * Routes are organized into separate files for better maintainability:
 *
 * - web.php: HTML pages using Plates templates
 * - api.php: JSON API endpoints
 */

declare(strict_types=1);

use Slim\App;

return function (App $app) {
    // Load Web Routes (HTML pages with Plates templates)
    $webRoutes = require __DIR__ . '/routes/web.php';
    $webRoutes($app);

    // Load API Routes (JSON responses)
    $apiRoutes = require __DIR__ . '/routes/api.php';
    $apiRoutes($app);
};
