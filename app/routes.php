<?php

declare(strict_types=1);

use App\Application\Actions\User\ListUsersAction;
use App\Application\Actions\User\ViewUserAction;
use App\Application\Actions\Article\ListArticlesAction;
use App\Application\Actions\Article\ViewArticleAction;
use App\Application\Actions\Article\ViewArticleBySkuAction;
use App\Application\Actions\Security\FileAccessAction;
use App\Application\Actions\Security\ListFilesAction;
use App\Application\Actions\Pages\HomeAction;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\App;
use Slim\Interfaces\RouteCollectorProxyInterface as Group;

// Set the "id" pattern
$id = '/{id:[0-9]+}';

return function (App $app) use ($id) {
    $app->options('/{routes:.*}', function (Request $request, Response $response) {
        // CORS Pre-Flight OPTIONS Request Handler
        return $response;
    });

    // Homepage with Plates template
    $app->get('/', HomeAction::class)->setName('home');

    $app->group('/user', function (Group $group) use ($id) {
        $group->get('s', ListUsersAction::class)->setName('users-list');
        $group->get($id, ViewUserAction::class)->setName('user-view');
    });

    $app->group('/article', function (Group $group) use ($id) {
        $group->get('s', ListArticlesAction::class)->setName('articles-list');
        $group->get($id, ViewArticleAction::class)->setName('article-view');
        $group->get('/sku/{sku:[A-Z0-9\-_]+}', ViewArticleBySkuAction::class)->setName('article-view-by-sku');
    });

    $app->group('/security', function (Group $group) {
        $group->get('/file', FileAccessAction::class)->setName('security-file-access');
        $group->get('/list', ListFilesAction::class)->setName('security-list-files');
    });
};
