<?php

/**
 * API Routes
 *
 * These routes return JSON responses and are intended for API consumption.
 * All routes are prefixed with /api/v1/
 */

declare(strict_types=1);

use App\Application\Actions\User\ListUsersAction;
use App\Application\Actions\User\ViewUserAction;
use App\Application\Actions\Article\ListArticlesAction;
use App\Application\Actions\Article\ViewArticleAction;
use App\Application\Actions\Article\ViewArticleBySkuAction;
use App\Application\Actions\Security\FileAccessAction;
use App\Application\Actions\Security\ListFilesAction;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\App;
use Slim\Interfaces\RouteCollectorProxyInterface as Group;

// Set the "id" pattern
$id = '/{id:[0-9]+}';

return function (App $app) use ($id) {
    // CORS Pre-Flight OPTIONS Request Handler for API
    $app->options('/api/{routes:.*}', function (Request $request, Response $response) {
        return $response
            ->withHeader('Access-Control-Allow-Origin', '*')
            ->withHeader('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Accept, Origin, Authorization')
            ->withHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    });

    // API v1 routes
    $app->group('/api/v1', function (Group $group) use ($id) {

        // User API endpoints
        $group->group('/users', function (Group $group) use ($id) {
            $group->get('', ListUsersAction::class)->setName('api-users-list');
            $group->get($id, ViewUserAction::class)->setName('api-user-view');
        });

        // Article API endpoints
        $group->group('/articles', function (Group $group) use ($id) {
            $group->get('', ListArticlesAction::class)->setName('api-articles-list');
            $group->get($id, ViewArticleAction::class)->setName('api-article-view');
            $group->get('/sku/{sku:[A-Z0-9\-_]+}', ViewArticleBySkuAction::class)->setName('api-article-view-by-sku');
        });

        // Security API endpoints (for demonstration)
        $group->group('/security', function (Group $group) {
            $group->get('/file', FileAccessAction::class)->setName('api-security-file-access');
            $group->get('/files', ListFilesAction::class)->setName('api-security-list-files');
        });
    });

    // Legacy API routes (for backward compatibility)
    // These will be deprecated in favor of /api/v1/ routes
    $app->group('/legacy', function (Group $group) use ($id) {
        $group->group('/users', function (Group $group) use ($id) {
            $group->get('', ListUsersAction::class)->setName('legacy-users-list');
            $group->get($id, ViewUserAction::class)->setName('legacy-user-view');
        });

        $group->group('/articles', function (Group $group) use ($id) {
            $group->get('', ListArticlesAction::class)->setName('legacy-articles-list');
            $group->get($id, ViewArticleAction::class)->setName('legacy-article-view');
            $group->get('/sku/{sku:[A-Z0-9\-_]+}', ViewArticleBySkuAction::class)->setName('legacy-article-view-by-sku');
        });

        $group->group('/security', function (Group $group) {
            $group->get('/file', FileAccessAction::class)->setName('legacy-security-file-access');
            $group->get('/list', ListFilesAction::class)->setName('legacy-security-list-files');
        });
    });
};
