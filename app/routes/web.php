<?php

/**
 * Web Routes
 *
 * These routes return HTML responses using Plates templates.
 * Intended for browser consumption with full HTML pages.
 */

declare(strict_types=1);

use App\Application\Actions\Pages\HomeAction;
use App\Application\Actions\Pages\UsersPageAction;
use App\Application\Actions\Pages\ArticlesPageAction;
use App\Application\Actions\Pages\UserDetailPageAction;
use App\Application\Actions\Pages\ArticleDetailPageAction;
use App\Application\Actions\Pages\SecurityDemoPageAction;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\App;
use Slim\Interfaces\RouteCollectorProxyInterface as Group;

// Set the "id" pattern
$id = '/{id:[0-9]+}';

return function (App $app) use ($id) {

    // Homepage
    $app->get('/', HomeAction::class)->setName('home');

    // About page
    $app->get('/about', function (Request $request, Response $response) {
        $response->getBody()->write('<h1>About Page</h1><p>This will be implemented with Plates template.</p>');
        return $response->withHeader('Content-Type', 'text/html');
    })->setName('about');

    // Users web pages
    $app->group('/users', function (Group $group) use ($id) {
        $group->get('', UsersPageAction::class)->setName('users-page');
        $group->get($id, UserDetailPageAction::class)->setName('user-detail-page');
    });

    // Articles web pages
    $app->group('/articles', function (Group $group) use ($id) {
        $group->get('', ArticlesPageAction::class)->setName('articles-page');
        $group->get($id, ArticleDetailPageAction::class)->setName('article-detail-page');
        $group->get('/sku/{sku:[A-Z0-9\-_]+}', ArticleDetailPageAction::class)->setName('article-detail-by-sku-page');
    });

    // Security demo pages
    $app->group('/demo', function (Group $group) {
        $group->get('/security', SecurityDemoPageAction::class)->setName('security-demo-page');
        $group->get('/security/file', function (Request $request, Response $response) {
            // Redirect to security demo with file parameter
            $queryString = $request->getUri()->getQuery();
            return $response
                ->withHeader('Location', '/demo/security?' . $queryString)
                ->withStatus(302);
        })->setName('security-file-demo');
    });

    // Static pages
    $app->get('/contact', function (Request $request, Response $response) {
        $response->getBody()->write('<h1>Contact Page</h1><p>This will be implemented with Plates template.</p>');
        return $response->withHeader('Content-Type', 'text/html');
    })->setName('contact');

    // Documentation pages
    $app->group('/docs', function (Group $group) {
        $group->get('', function (Request $request, Response $response) {
            $response->getBody()->write('<h1>Documentation</h1><p>API and usage documentation.</p>');
            return $response->withHeader('Content-Type', 'text/html');
        })->setName('docs-index');

        $group->get('/api', function (Request $request, Response $response) {
            $response->getBody()->write('<h1>API Documentation</h1><p>RESTful API endpoints documentation.</p>');
            return $response->withHeader('Content-Type', 'text/html');
        })->setName('docs-api');
    });

    // Error testing routes (for development)
    $app->get('/test/404', function (Request $request, Response $response) {
        throw new \Slim\Exception\HttpNotFoundException($request);
    })->setName('test-404');

    $app->get('/test/500', function (Request $request, Response $response) {
        throw new \Exception('Test server error');
    })->setName('test-500');
};
