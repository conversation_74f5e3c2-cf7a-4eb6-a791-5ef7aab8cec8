<?php

declare(strict_types=1);

use App\Application\Settings\Settings;
use App\Application\Settings\SettingsInterface;
use DI\ContainerBuilder;
use Monolog\Logger;

// Load paths helper functions
require_once __DIR__ . '/paths.php';

return function (ContainerBuilder $containerBuilder) {

    // Global Settings Object
    $containerBuilder->addDefinitions([
        SettingsInterface::class => function () {
            return new Settings([
                'displayErrorDetails' => true, // Should be set to false in production
                'logError' => false,
                'logErrorDetails' => false,
                'logger' => [
                    'name' => 'slim-app',
                    'path' => isset($_ENV['docker']) ? 'php://stdout' : getLogPath('app.log'),
                    'level' => Logger::DEBUG,
                ],

                // Telegram Error Logger
                'telegram' => [
                    'token' => $_ENV['TELEGRAM_BOT_TOKEN'] ?? '',
                    'channel' => $_ENV['TELEGRAM_CHANNEL'] ?? '',
                    'app_name' => $_ENV['TELEGRAM_APP_NAME'] ?? 'Slim4 App',
                    'level' => \Monolog\Level::Error,
                ],

                // Doctrine ORM settings
                'doctrine' => [
                    // if true, metadata caching is forcefully disabled
                    'dev_mode' => true,

                    // you should add any other path containing annotated entity classes
                    'metadata_dirs' => getMetadataDirs(),

                    'proxy_dir' => getProxyPath(),

                    'connections' => [
                        'default' => [
                            'driver' => 'pdo_sqlite',
                            'path' => getDatabasePath('user.db'),
                            'charset' => 'utf8'
                        ],
                        'article' => [
                            'driver' => 'pdo_sqlite',
                            'path' => getDatabasePath('article.db'),
                            'charset' => 'utf8'
                        ],
                    ],
                ],
            ]);
        }
    ]);
};
