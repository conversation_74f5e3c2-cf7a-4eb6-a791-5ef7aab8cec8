<?php

/**
 * Application Paths Configuration
 *
 * This file provides a centralized way to access application paths
 * using the responsive-sk/slim4-paths package
 */

declare(strict_types=1);

use ResponsiveSk\Slim4Paths\Paths;

// Create global Paths instance
$appPaths = new Paths(dirname(__DIR__));

/**
 * Get application paths instance
 */
function getAppPaths(): Paths
{
    global $appPaths;

    // Initialize if not set (for tests)
    if (!isset($appPaths)) {
        $appPaths = new Paths(dirname(__DIR__));
    }

    return $appPaths;
}

/**
 * Get specific path with security validation
 */
function getSecurePath(string $relativePath = ''): string
{
    $paths = getAppPaths();
    return $paths->path($relativePath);
}

/**
 * Get database path (databases are in var/ not var/storage/)
 */
function getDatabasePath(string $dbName): string
{
    $paths = getAppPaths();
    return $paths->path('var/' . $dbName);
}

/**
 * Get log path (logs are in var/logs/ not logs/)
 */
function getLogPath(string $logFile = 'app.log'): string
{
    $paths = getAppPaths();
    return $paths->path('var/logs/' . $logFile);
}

/**
 * Get cache path
 */
function getCachePath(string $cacheDir = ''): string
{
    $paths = getAppPaths();
    return $paths->cache($cacheDir);
}

/**
 * Get proxy path for Doctrine (in var/proxy)
 */
function getProxyPath(): string
{
    $paths = getAppPaths();
    return $paths->path('var/proxy');
}

/**
 * Get metadata directories for Doctrine
 */
function getMetadataDirs(): array
{
    $paths = getAppPaths();
    return [$paths->path('src/Domain')];
}
