<?php

declare(strict_types=1);

use ResponsiveSk\Slim4Paths\Paths;

/**
 * Application Paths Configuration
 * 
 * This file provides a centralized way to access application paths
 * using the responsive-sk/slim4-paths package
 */

// Create global Paths instance
$appPaths = new Paths(__DIR__ . '/..');

/**
 * Get application paths instance
 */
function getAppPaths(): Paths
{
    global $appPaths;
    return $appPaths;
}

/**
 * Get specific path with security validation
 */
function getSecurePath(string $relativePath = ''): string
{
    $paths = getAppPaths();
    return $paths->path($relativePath);
}

/**
 * Get database path (databases are in var/ not var/storage/)
 */
function getDatabasePath(string $dbName): string
{
    $paths = getAppPaths();
    return $paths->path('var/' . $dbName);
}

/**
 * Get log path
 */
function getLogPath(string $logFile = 'app.log'): string
{
    $paths = getAppPaths();
    return $paths->logs($logFile);
}

/**
 * Get cache path
 */
function getCachePath(string $cacheDir = ''): string
{
    $paths = getAppPaths();
    return $paths->cache($cacheDir);
}

/**
 * Get proxy path for Doctrine
 */
function getProxyPath(): string
{
    $paths = getAppPaths();
    return $paths->storage('proxy');
}

/**
 * Get metadata directories for Doctrine
 */
function getMetadataDirs(): array
{
    $paths = getAppPaths();
    return [$paths->path('src/Domain')];
}
