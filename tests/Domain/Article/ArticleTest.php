<?php

declare(strict_types=1);

namespace Tests\Domain\Article;

use App\Domain\Article\Article;
use App\Domain\Article\ArticleType;
use App\Domain\Article\ArticleRepository;
use Doctrine\ORM\EntityManager;
use Tests\TestCase;

class ArticleTest extends TestCase
{
    public function testArticleJsonSerialize()
    {
        $app = $this->getAppInstance();
        $container = $app->getContainer();

        /** @var EntityManager $entityManager */
        $entityManager = $container->get('ArticleEntityManager');

        $articleRepository = new ArticleRepository($entityManager);

        $article = $articleRepository->findArticleOfId(1);

        if ($article) {
            $expectedKeys = [
                'id', 'title', 'description', 'type', 'typeLabel', 'sku',
                'price', 'currency', 'formattedPrice', 'deliveryMethod',
                'deliveryTimeDays', 'stockQuantity', 'isActive', 'isInStock',
                'requiresDelivery', 'createdAt', 'updatedAt'
            ];

            $serialized = $article->jsonSerialize();
            
            foreach ($expectedKeys as $key) {
                $this->assertArrayHasKey($key, $serialized);
            }

            $this->assertEquals($article->getId(), $serialized['id']);
            $this->assertEquals($article->getTitle(), $serialized['title']);
            $this->assertEquals($article->getType()->value, $serialized['type']);
            $this->assertEquals($article->getType()->getLabel(), $serialized['typeLabel']);
            $this->assertEquals($article->getSku(), $serialized['sku']);
        } else {
            $this->markTestSkipped('No article found with ID 1. Run article seeder first.');
        }
    }

    public function testArticleTypeEnum()
    {
        // Test enum values
        $this->assertEquals('digital_art', ArticleType::DIGITAL_ART->value);
        $this->assertEquals('physical_art', ArticleType::PHYSICAL_ART->value);
        $this->assertEquals('blog_art', ArticleType::BLOG_ART->value);

        // Test enum methods
        $digitalArt = ArticleType::DIGITAL_ART;
        $this->assertEquals('Digital Art', $digitalArt->getLabel());
        $this->assertTrue($digitalArt->isDigitalContent());
        $this->assertFalse($digitalArt->requiresPhysicalDelivery());

        $painting = ArticleType::PAINTING;
        $this->assertEquals('Painting', $painting->getLabel());
        $this->assertFalse($painting->isDigitalContent());
        $this->assertTrue($painting->requiresPhysicalDelivery());

        // Test static methods
        $allTypes = ArticleType::getAllTypes();
        $this->assertGreaterThan(5, count($allTypes));
        $this->assertContains(ArticleType::DIGITAL_ART, $allTypes);

        $physicalTypes = ArticleType::getPhysicalTypes();
        $this->assertContains(ArticleType::PAINTING, $physicalTypes);
        $this->assertNotContains(ArticleType::DIGITAL_ART, $physicalTypes);

        $digitalTypes = ArticleType::getDigitalTypes();
        $this->assertContains(ArticleType::DIGITAL_ART, $digitalTypes);
        $this->assertNotContains(ArticleType::PAINTING, $digitalTypes);
    }

    public function testArticleCreation()
    {
        $article = new Article(
            null,
            'Test Article',
            ArticleType::DIGITAL_ART,
            'TEST-001',
            'Test description',
            99.99,
            'EUR',
            'Digital Download',
            1,
            10,
            true
        );

        $this->assertEquals('Test Article', $article->getTitle());
        $this->assertEquals(ArticleType::DIGITAL_ART, $article->getType());
        $this->assertEquals('TEST-001', $article->getSku());
        $this->assertEquals(99.99, $article->getPrice());
        $this->assertEquals('EUR', $article->getCurrency());
        $this->assertEquals('Digital Download', $article->getDeliveryMethod());
        $this->assertEquals(1, $article->getDeliveryTimeDays());
        $this->assertEquals(10, $article->getStockQuantity());
        $this->assertTrue($article->isActive());
        $this->assertTrue($article->isInStock());
        $this->assertFalse($article->requiresDelivery());
        $this->assertEquals('99.99 EUR', $article->getFormattedPrice());
    }

    public function testArticleWithNullPrice()
    {
        $article = new Article(
            null,
            'Free Article',
            ArticleType::BLOG_ART,
            'FREE-001',
            'Free content',
            null,
            'EUR',
            null,
            null,
            999,
            true
        );

        $this->assertNull($article->getPrice());
        $this->assertEquals('Free', $article->getFormattedPrice());
        $this->assertFalse($article->requiresDelivery());
    }
}
