<?php

declare(strict_types=1);

namespace Tests\Fixtures;

use App\Domain\Article\Article;
use App\Domain\Article\ArticleType;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Article fixtures for testing
 * Provides methods to create test articles for unit and integration tests
 */
class ArticleFixtures
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Create a single test article
     */
    public function createArticle(
        ?int $id = null,
        string $title = 'Test Article',
        ArticleType $type = ArticleType::DIGITAL_ART,
        string $sku = 'TEST-001',
        ?string $description = 'Test article description',
        ?float $price = 99.99,
        string $currency = 'EUR',
        ?string $deliveryMethod = 'Digital Download',
        ?int $deliveryTimeDays = 1,
        int $stockQuantity = 10,
        bool $isActive = true
    ): Article {
        $article = new Article(
            $id,
            $title,
            $type,
            $sku,
            $description,
            $price,
            $currency,
            $deliveryMethod,
            $deliveryTimeDays,
            $stockQuantity,
            $isActive
        );
        $this->entityManager->persist($article);
        return $article;
    }

    /**
     * Create multiple test articles and return them
     */
    public function createMultipleArticles(int $count = 5): array
    {
        $articles = [];
        $baseData = [
            [
                'title' => 'Digital Art Masterpiece',
                'type' => ArticleType::DIGITAL_ART,
                'sku' => 'DIG-001',
                'price' => 299.99,
                'deliveryMethod' => 'Digital Download'
            ],
            [
                'title' => 'Oil Painting Landscape',
                'type' => ArticleType::PAINTING,
                'sku' => 'PAINT-001',
                'price' => 1299.50,
                'deliveryMethod' => 'Courier'
            ],
            [
                'title' => 'Bronze Sculpture',
                'type' => ArticleType::SCULPTURE,
                'sku' => 'SCULP-001',
                'price' => 2499.00,
                'deliveryMethod' => 'Special Delivery'
            ],
            [
                'title' => 'Photography Print',
                'type' => ArticleType::PHOTOGRAPHY,
                'sku' => 'PHOTO-001',
                'price' => 199.99,
                'deliveryMethod' => 'Standard Post'
            ],
            [
                'title' => 'Art Blog Post',
                'type' => ArticleType::BLOG_ART,
                'sku' => 'BLOG-001',
                'price' => null,
                'deliveryMethod' => null
            ]
        ];

        for ($i = 0; $i < min($count, count($baseData)); $i++) {
            $data = $baseData[$i];
            $article = $this->createArticle(
                null,
                $data['title'],
                $data['type'],
                $data['sku'],
                'Test description for ' . $data['title'],
                $data['price'],
                'EUR',
                $data['deliveryMethod'],
                $data['deliveryMethod'] ? 7 : null,
                $data['price'] ? 10 : 999
            );
            $articles[] = $article;
        }

        return $articles;
    }

    /**
     * Create articles of specific type
     */
    public function createArticlesByType(ArticleType $type, int $count = 3): array
    {
        $articles = [];
        for ($i = 1; $i <= $count; $i++) {
            $article = $this->createArticle(
                null,
                $type->getLabel() . " #{$i}",
                $type,
                strtoupper($type->value) . "-{$i:03d}",
                "Test {$type->getLabel()} #{$i}",
                $type->isDigitalContent() ? 99.99 : 299.99,
                'EUR',
                $type->requiresPhysicalDelivery() ? 'Courier' : 'Digital Download',
                $type->requiresPhysicalDelivery() ? 7 : 1,
                $type->isDigitalContent() ? 100 : 5
            );
            $articles[] = $article;
        }
        return $articles;
    }

    /**
     * Create a specific article by ID (useful for tests that expect specific IDs)
     */
    public function createArticleWithId(int $id): Article
    {
        $articleData = [
            1 => ['Digital Art Test', ArticleType::DIGITAL_ART, 'DIG-TEST-001'],
            2 => ['Painting Test', ArticleType::PAINTING, 'PAINT-TEST-002'],
            3 => ['Sculpture Test', ArticleType::SCULPTURE, 'SCULP-TEST-003'],
            4 => ['Photo Test', ArticleType::PHOTOGRAPHY, 'PHOTO-TEST-004'],
            5 => ['Blog Test', ArticleType::BLOG_ART, 'BLOG-TEST-005']
        ];

        $data = $articleData[$id] ?? ['Test Article ' . $id, ArticleType::DIGITAL_ART, 'TEST-' . $id];
        return $this->createArticle(
            $id,
            $data[0],
            $data[1],
            $data[2],
            'Test description for article ' . $id,
            $data[1]->isDigitalContent() ? null : 199.99
        );
    }

    /**
     * Clear all articles from the database
     */
    public function clearArticles(): void
    {
        $articles = $this->entityManager->getRepository(Article::class)->findAll();
        foreach ($articles as $article) {
            $this->entityManager->remove($article);
        }
    }

    /**
     * Load standard test dataset
     */
    public function loadStandardDataset(): array
    {
        $this->clearArticles();
        $articles = $this->createMultipleArticles(10);
        $this->entityManager->flush();
        return $articles;
    }

    /**
     * Create articles with different stock levels for testing
     */
    public function createArticlesWithStock(): array
    {
        $articles = [];
        
        // In stock article
        $articles[] = $this->createArticle(
            null,
            'In Stock Article',
            ArticleType::DIGITAL_ART,
            'STOCK-001',
            'Article with stock',
            99.99,
            'EUR',
            'Digital Download',
            1,
            50
        );
        
        // Out of stock article
        $articles[] = $this->createArticle(
            null,
            'Out of Stock Article',
            ArticleType::PAINTING,
            'STOCK-002',
            'Article without stock',
            299.99,
            'EUR',
            'Courier',
            7,
            0
        );
        
        return $articles;
    }

    /**
     * Get sample article data as array (without persisting)
     */
    public static function getSampleArticleData(): array
    {
        return [
            [
                'title' => 'Digital Art Sample',
                'type' => ArticleType::DIGITAL_ART,
                'sku' => 'DIG-SAMPLE-001',
                'price' => 199.99,
                'description' => 'Sample digital artwork'
            ],
            [
                'title' => 'Physical Art Sample',
                'type' => ArticleType::PAINTING,
                'sku' => 'PAINT-SAMPLE-002',
                'price' => 899.99,
                'description' => 'Sample physical painting'
            ],
            [
                'title' => 'Blog Article Sample',
                'type' => ArticleType::BLOG_ART,
                'sku' => 'BLOG-SAMPLE-003',
                'price' => null,
                'description' => 'Sample blog article'
            ]
        ];
    }
}
