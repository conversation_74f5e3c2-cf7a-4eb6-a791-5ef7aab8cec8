<?php

declare(strict_types=1);

namespace Tests\Fixtures;

use App\Domain\User\User;
use Doctrine\ORM\EntityManagerInterface;

/**
 * User fixtures for testing
 * Provides methods to create test users for unit and integration tests
 */
class UserFixtures
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Create a single test user
     */
    public function createUser(
        ?int $id = null,
        string $username = 'test.user',
        string $firstName = 'Test',
        string $lastName = 'User'
    ): User {
        $user = new User($id, $username, $firstName, $lastName);
        $this->entityManager->persist($user);
        return $user;
    }

    /**
     * Create multiple test users and return them
     */
    public function createMultipleUsers(int $count = 5): array
    {
        $users = [];
        $baseData = [
            ['john.doe', '<PERSON>', '<PERSON><PERSON>'],
            ['jane.smith', '<PERSON>', '<PERSON>'],
            ['bob.wilson', '<PERSON>', '<PERSON>'],
            ['alice.johnson', '<PERSON>', '<PERSON>'],
            ['charlie.brown', '<PERSON>', '<PERSON>'],
            ['diana.prince', '<PERSON>', '<PERSON>'],
            ['peter.parker', '<PERSON>', '<PERSON>'],
            ['mary.jane', '<PERSON>', '<PERSON>'],
            ['bruce.wayne', '<PERSON>', '<PERSON>'],
            ['clark.kent', '<PERSON>', '<PERSON>']
        ];

        for ($i = 0; $i < min($count, count($baseData)); $i++) {
            $userData = $baseData[$i];
            $user = $this->createUser(null, $userData[0], $userData[1], $userData[2]);
            $users[] = $user;
        }

        return $users;
    }

    /**
     * Create a specific user by ID (useful for tests that expect specific IDs)
     */
    public function createUserWithId(int $id): User
    {
        $userData = [
            1 => ['john.doe', 'John', 'Doe'],
            2 => ['jane.smith', 'Jane', 'Smith'],
            3 => ['bob.wilson', 'Bob', 'Wilson'],
            4 => ['alice.johnson', 'Alice', 'Johnson'],
            5 => ['charlie.brown', 'Charlie', 'Brown']
        ];

        $data = $userData[$id] ?? ['user' . $id, 'User', 'Number' . $id];
        return $this->createUser($id, $data[0], $data[1], $data[2]);
    }

    /**
     * Clear all users from the database
     */
    public function clearUsers(): void
    {
        $users = $this->entityManager->getRepository(User::class)->findAll();
        foreach ($users as $user) {
            $this->entityManager->remove($user);
        }
    }

    /**
     * Load standard test dataset
     */
    public function loadStandardDataset(): array
    {
        $this->clearUsers();
        $users = $this->createMultipleUsers(10);
        $this->entityManager->flush();
        return $users;
    }

    /**
     * Get sample user data as array (without persisting)
     */
    public static function getSampleUserData(): array
    {
        return [
            ['username' => 'john.doe', 'firstName' => 'John', 'lastName' => 'Doe'],
            ['username' => 'jane.smith', 'firstName' => 'Jane', 'lastName' => 'Smith'],
            ['username' => 'bob.wilson', 'firstName' => 'Bob', 'lastName' => 'Wilson'],
            ['username' => 'alice.johnson', 'firstName' => 'Alice', 'lastName' => 'Johnson'],
            ['username' => 'charlie.brown', 'firstName' => 'Charlie', 'lastName' => 'Brown'],
            ['username' => 'diana.prince', 'firstName' => 'Diana', 'lastName' => 'Prince'],
            ['username' => 'peter.parker', 'firstName' => 'Peter', 'lastName' => 'Parker'],
            ['username' => 'mary.jane', 'firstName' => 'Mary', 'lastName' => 'Jane'],
            ['username' => 'bruce.wayne', 'firstName' => 'Bruce', 'lastName' => 'Wayne'],
            ['username' => 'clark.kent', 'firstName' => 'Clark', 'lastName' => 'Kent']
        ];
    }
}
