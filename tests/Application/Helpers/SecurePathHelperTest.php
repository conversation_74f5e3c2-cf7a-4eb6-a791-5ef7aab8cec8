<?php

declare(strict_types=1);

namespace Tests\Application\Helpers;

use App\Application\Helpers\SecurePathHelper;
use ResponsiveSk\Slim4Paths\Paths;
use InvalidArgumentException;
use Tests\TestCase;

class SecurePathHelperTest extends TestCase
{
    private SecurePathHelper $helper;
    private string $testDir;

    protected function setUp(): void
    {
        parent::setUp();

        $paths = new Paths(__DIR__ . '/../../..');
        $this->helper = new SecurePathHelper($paths);

        // Create test directory
        $this->testDir = sys_get_temp_dir() . '/secure-path-test-' . uniqid();
        mkdir($this->testDir, 0755, true);

        // Add test directory as allowed
        $this->helper->addAllowedDirectory('test', $this->testDir);

        // Create test file
        file_put_contents($this->testDir . '/test-file.txt', 'Test content');
    }

    protected function tearDown(): void
    {
        // Clean up test directory
        if (is_dir($this->testDir)) {
            $files = glob($this->testDir . '/*');
            if ($files !== false) {
                array_map('unlink', $files);
            }
            rmdir($this->testDir);
        }

        parent::tearDown();
    }

    public function testSecurePathWithValidFile(): void
    {
        $securePath = $this->helper->securePath('test-file.txt', 'test');

        $this->assertNotEmpty($this->testDir);
        $this->assertTrue(str_starts_with($securePath, $this->testDir));
        $this->assertStringEndsWith('test-file.txt', $securePath);
    }

    public function testSecurePathBlocksTraversal(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Path traversal attempt detected');

        $this->helper->securePath('../../../etc/passwd', 'test');
    }

    public function testSecurePathBlocksUrlEncodedTraversal(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Path traversal attempt detected');

        $this->helper->securePath('..%2f..%2fetc%2fpasswd', 'test');
    }

    public function testSecurePathWithInvalidBaseDirectory(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Base directory \'invalid\' is not allowed');

        $this->helper->securePath('test-file.txt', 'invalid');
    }

    public function testFileExists(): void
    {
        $this->assertTrue($this->helper->fileExists('test-file.txt', 'test'));
        $this->assertFalse($this->helper->fileExists('non-existent.txt', 'test'));
    }

    public function testFileExistsWithTraversal(): void
    {
        $this->assertFalse($this->helper->fileExists('../../../etc/passwd', 'test'));
    }

    public function testReadFile(): void
    {
        $content = $this->helper->readFile('test-file.txt', 'test');

        $this->assertEquals('Test content', $content);
    }

    public function testReadNonExistentFile(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('does not exist');

        $this->helper->readFile('non-existent.txt', 'test');
    }

    public function testWriteFile(): void
    {
        $result = $this->helper->writeFile('new-file.txt', 'New content', 'test');

        $this->assertTrue($result);
        $this->assertTrue($this->helper->fileExists('new-file.txt', 'test'));
        $this->assertEquals('New content', $this->helper->readFile('new-file.txt', 'test'));
    }

    public function testWriteFileInSubdirectory(): void
    {
        // Create subdirectory first
        mkdir($this->testDir . '/subdir', 0755, true);

        $result = $this->helper->writeFile('subdir/new-file.txt', 'Subdir content', 'test');

        $this->assertTrue($result);
        $this->assertTrue($this->helper->fileExists('subdir/new-file.txt', 'test'));
    }

    public function testListFiles(): void
    {
        // Create additional test files
        file_put_contents($this->testDir . '/file1.txt', 'Content 1');
        file_put_contents($this->testDir . '/file2.txt', 'Content 2');
        mkdir($this->testDir . '/subdir');

        $files = $this->helper->listFiles('', 'test');

        $this->assertIsArray($files);
        $this->assertGreaterThanOrEqual(3, count($files)); // At least test-file.txt, file1.txt, file2.txt

        // Check file structure
        $fileNames = array_column($files, 'name');
        $this->assertContains('test-file.txt', $fileNames);
        $this->assertContains('file1.txt', $fileNames);
        $this->assertContains('subdir', $fileNames);
    }

    public function testListNonExistentDirectory(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('does not exist');

        $this->helper->listFiles('non-existent-dir', 'test');
    }

    public function testGetAllowedDirectories(): void
    {
        $allowedDirs = $this->helper->getAllowedDirectories();

        $this->assertIsArray($allowedDirs);
        $this->assertArrayHasKey('test', $allowedDirs);
        $this->assertEquals($this->testDir, $allowedDirs['test']);
    }
}
