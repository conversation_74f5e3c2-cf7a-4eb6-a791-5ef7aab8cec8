<?php

declare(strict_types=1);

namespace Tests\Application\Middleware;

use App\Application\Middleware\PathSecurityMiddleware;
use ResponsiveSk\Slim4Paths\Paths;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\RequestHandlerInterface as RequestHandler;
use Slim\Exception\HttpBadRequestException;
use Slim\Psr7\Factory\ServerRequestFactory;
use Slim\Psr7\Response;
use Tests\TestCase;

class PathSecurityMiddlewareTest extends TestCase
{
    private PathSecurityMiddleware $middleware;
    private RequestHandler $handler;

    protected function setUp(): void
    {
        parent::setUp();

        $paths = new Paths(__DIR__ . '/../../..');
        $this->middleware = new PathSecurityMiddleware($paths);

        // Mock request handler
        $this->handler = new class implements RequestHandler {
            public function handle(Request $request): \Psr\Http\Message\ResponseInterface
            {
                return new Response();
            }
        };
    }

    public function testAllowsNormalRequests(): void
    {
        $request = (new ServerRequestFactory())->createServerRequest('GET', '/test')
            ->withQueryParams(['file' => 'normal-file.txt']);

        $response = $this->middleware->process($request, $this->handler);

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function testBlocksPathTraversalInQueryParams(): void
    {
        $this->expectException(HttpBadRequestException::class);
        $this->expectExceptionMessage('Path traversal attempt detected');

        $request = (new ServerRequestFactory())->createServerRequest('GET', '/test')
            ->withQueryParams(['file' => '../../../etc/passwd']);

        $this->middleware->process($request, $this->handler);
    }

    public function testBlocksUrlEncodedPathTraversal(): void
    {
        $this->expectException(HttpBadRequestException::class);
        $this->expectExceptionMessage('Path traversal attempt detected');

        $request = (new ServerRequestFactory())->createServerRequest('GET', '/test')
            ->withQueryParams(['file' => '..%2f..%2f..%2fetc%2fpasswd']);

        $this->middleware->process($request, $this->handler);
    }

    public function testBlocksWindowsPathTraversal(): void
    {
        $this->expectException(HttpBadRequestException::class);
        $this->expectExceptionMessage('Path traversal attempt detected');

        $request = (new ServerRequestFactory())->createServerRequest('GET', '/test')
            ->withQueryParams(['file' => '..\\..\\..\\windows\\system32\\config\\sam']);

        $this->middleware->process($request, $this->handler);
    }

    public function testBlocksPathTraversalInPostData(): void
    {
        $this->expectException(HttpBadRequestException::class);
        $this->expectExceptionMessage('Path traversal attempt detected');

        $request = (new ServerRequestFactory())->createServerRequest('POST', '/test')
            ->withParsedBody(['path' => '../sensitive-file.txt']);

        $this->middleware->process($request, $this->handler);
    }

    public function testBlocksNestedPathTraversal(): void
    {
        $this->expectException(HttpBadRequestException::class);
        $this->expectExceptionMessage('Path traversal attempt detected');

        $request = (new ServerRequestFactory())->createServerRequest('GET', '/test')
            ->withQueryParams([
                'data' => [
                    'file' => '../../../etc/passwd',
                    'other' => 'normal-value'
                ]
            ]);

        $this->middleware->process($request, $this->handler);
    }

    public function testAllowsCustomAllowedDirectories(): void
    {
        $this->middleware->addAllowedDirectory('/tmp');
        $allowedDirs = $this->middleware->getAllowedDirectories();

        $this->assertContains('/tmp', $allowedDirs);
    }

    public function testAllowsCustomBlockedPatterns(): void
    {
        $this->middleware->addBlockedPattern('custom-pattern');
        $blockedPatterns = $this->middleware->getBlockedPatterns();

        $this->assertContains('custom-pattern', $blockedPatterns);
    }

    public function testBlocksCustomPattern(): void
    {
        $this->expectException(HttpBadRequestException::class);
        $this->expectExceptionMessage('Path traversal attempt detected');

        $this->middleware->addBlockedPattern('forbidden');

        $request = (new ServerRequestFactory())->createServerRequest('GET', '/test')
            ->withQueryParams(['file' => 'some-forbidden-path']);

        $this->middleware->process($request, $this->handler);
    }
}
