# Slim Framework 4 Skeleton Application with Doctrine ORM 3

[![Build Status](https://github.com/AntoninoM90/slim4-doctrine-skeleton/workflows/Tests/badge.svg)](https://github.com/AntoninoM90/slim4-doctrine-skeleton/actions)

Use this skeleton application to quickly setup and start working on a new Slim Framework 4 application. This application uses the latest Slim 4 with Slim PSR-7 implementation and PHP-DI container implementation. It also uses the Monolog logger.

This skeleton application was built for Composer. This makes setting up a new Slim Framework application quick and easy.

## Install the Application

Run this command from the directory in which you want to clone the Slim Framework application skeleton. You will require PHP 8.2 or newer.

```bash
git clone https://github.com/AntoninoM90/slim4-doctrine3-skeleton.git
```

You'll want to:

* Point your virtual host document root to your new application's `public/` directory.
* Ensure `logs/` is web writable.

To run the application in development, you can run these commands

```bash
cd [my-app-name]
composer start
```

Or you can use `docker-compose` to run the app with `docker`, so you can run these commands:
```bash
cd [my-app-name]
docker-compose up -d
```
After that, open `http://localhost:8080` in your browser.

Run this command in the application directory to run the test suite

```bash
composer test
```

That's it! Now go build something cool.

## Database Test Data

This application includes comprehensive test data setup for the SQLite database.

### Quick Start with Test Data

1. **Complete setup (recommended for first time):**
   ```bash
   ./bin/setup-database.php
   ```

2. **Just add test users to existing database:**
   ```bash
   ./bin/seed-users.php          # Interactive with prompts
   ./bin/seed-users-silent.php   # Silent for automation
   ```

3. **Load SQL fixtures directly:**
   ```bash
   ./bin/load-sql-fixtures.php
   ```

### Available Scripts

- `bin/setup-database.php` - Creates schema and seeds data
- `bin/seed-users.php` - Interactive user seeder (10 users)
- `bin/seed-users-silent.php` - Silent user seeder (for CI/CD)
- `bin/load-sql-fixtures.php` - Loads SQL fixtures (20 users)

### Test Data Structure

The scripts create realistic test users with:
- Unique usernames (e.g., john.doe, jane.smith)
- First and last names
- Auto-generated IDs

### API Testing

After seeding data, test the endpoints:
```bash
# List all users
curl http://localhost:8080/users

# Get specific user
curl http://localhost:8080/user/1
```

### Test Fixtures for Unit Tests

Use the `UserFixtures` class in your tests:
```php
use Tests\Fixtures\UserFixtures;

$fixtures = new UserFixtures($entityManager);
$users = $fixtures->loadStandardDataset();
```

See `fixtures/README.md` for detailed documentation.

## Article Domain with Separate Database

This application now includes a complete Article domain with its own `article.db` database.

### Article Features

- **Multiple Article Types**: Blog articles, physical art, digital art, photography, sculptures, paintings, drawings, mixed media, collectibles, vintage items
- **Rich Data Model**: Price, SKU, delivery methods, stock management, timestamps
- **Separate Database**: Articles stored in `var/article.db` (separate from users)
- **Advanced Filtering**: Filter by type, stock status, search terms
- **SKU-based Lookup**: Find articles by unique SKU codes

### Article API Endpoints

```bash
# List all articles (with optional filters)
curl http://localhost:8080/articles
curl "http://localhost:8080/articles?type=digital_art"
curl "http://localhost:8080/articles?in_stock=1"
curl "http://localhost:8080/articles?search=painting"

# Get specific article by ID
curl http://localhost:8080/article/1

# Get article by SKU
curl http://localhost:8080/article/sku/DIG-ABS-001
```

### Article Database Setup

```bash
# Complete setup (recommended for first time)
./bin/setup-article-database.php

# Just seed articles to existing database
./bin/seed-articles.php

# Load SQL fixtures directly
./bin/load-article-sql-fixtures.php
```

### Article Types Available

- `blog_art` - Blog articles and written content
- `physical_art` - Physical artwork requiring delivery
- `digital_art` - Digital artwork delivered electronically
- `photography` - Photographic prints and digital photos
- `sculpture` - Three-dimensional sculptural works
- `painting` - Painted artwork on various mediums
- `drawing` - Hand-drawn artwork and sketches
- `mixed_media` - Artwork combining multiple mediums
- `collectible` - Collectible art pieces and limited editions
- `vintage` - Vintage and antique art pieces

## Code Quality Tools

This project includes comprehensive code quality tools for maintaining high standards.

### Quick Quality Check
```bash
# Complete quality check (coding standards + static analysis + tests)
./bin/quality-check.sh

# Fast check (skip tests)
./bin/quality-check.sh --fast

# Fix issues automatically
./bin/quality-check.sh --fix
```

### Individual Tools
```bash
# Coding Standards (PSR-12)
composer cs-check          # Check standards
composer cs-fix            # Fix automatically
./bin/cs-check.sh --diff   # Show what needs fixing

# Static Analysis (PHPStan level max)
composer phpstan           # Run analysis
./bin/phpstan.sh --baseline # Generate baseline
./bin/phpstan.sh --level=8  # Custom level

# Combined
composer quality           # All checks
composer quality-fix       # Fix and check
```

### Features
- ✅ **PSR-12 Compliance** - Automatic code formatting
- ✅ **PHPStan Level Max** - Highest static analysis level
- ✅ **Baseline Support** - Gradual improvement workflow
- ✅ **Automated Fixing** - Most issues fixed automatically
- ✅ **CI/CD Ready** - Perfect for automated pipelines

See `docs/QUALITY_TOOLS.md` for detailed documentation.

## Path Security Protection

This application includes comprehensive protection against path traversal attacks using `responsive-sk/slim4-paths`.

### Security Features
- ✅ **Path Traversal Protection** - Blocks `../`, `..%2f`, and other attack patterns
- ✅ **Directory Restrictions** - File access limited to allowed directories only
- ✅ **Real Path Validation** - Uses `realpath()` for secure path resolution
- ✅ **Automatic Logging** - Security violations logged for monitoring
- ✅ **Multiple Layers** - Middleware + Helper class for defense in depth

### Demo Endpoints
```bash
# Safe file access
curl "http://localhost:8080/security/file?file=demo/safe-file.txt&dir=public"

# Directory listing
curl "http://localhost:8080/security/list?path=demo&base=public"

# Path traversal attempt (blocked)
curl "http://localhost:8080/security/file?file=../../../etc/passwd&dir=public"
```

### Usage in Code
```php
// Safe file operations
$helper = $container->get(SecurePathHelper::class);
$content = $helper->readFile('file.txt', 'public');
$helper->writeFile('output.txt', 'data', 'storage');
$files = $helper->listFiles('uploads', 'public');
```

### Allowed Directories
- `public/` - Public web files and uploads
- `var/storage/` - Application storage
- `var/cache/` - Cache files
- `var/logs/` - Log files
- `templates/` - Template files
- `config/` - Configuration files

See `docs/PATH_SECURITY.md` for detailed documentation.

## Plates Templates

This application uses Plates template engine for rendering HTML views with modern, secure templating.

### Template Features
- ✅ **Native PHP syntax** - No new template language to learn
- ✅ **Template inheritance** - Layout system with sections
- ✅ **Component system** - Reusable template components
- ✅ **Automatic escaping** - XSS protection with `$this->e()`
- ✅ **Fast performance** - No compilation step needed

### Template Structure
```
templates/
├── layouts/app.php      # Main application layout
├── pages/home.php       # Homepage template
├── components/          # Reusable components
│   ├── header.php       # Navigation header
│   ├── footer.php       # Site footer
│   └── navigation.php   # Main navigation
├── articles/            # Article templates
│   ├── index.php        # Article listing
│   └── card.php         # Article card component
└── errors/              # Error page templates
    ├── 404.php          # Not found page
    └── error.php        # Generic error page
```

### Usage Example
```php
// In Action class
class HomeAction extends TemplateAction
{
    protected function action(): Response
    {
        return $this->render('pages::home', [
            'title' => 'Welcome',
            'features' => $this->getFeatures()
        ]);
    }
}
```

### Template Example
```php
<?php $this->layout('layouts::app', ['title' => 'Homepage']) ?>

<?php $this->start('main') ?>
<h1><?= $this->e($title) ?></h1>
<p><?= $this->e($description) ?></p>
<?php $this->stop() ?>
```

See `docs/PLATES_TEMPLATES.md` for detailed documentation.

## 📚 Documentation

This project includes comprehensive documentation for all implemented features:

### Core Documentation
- **[Project Summary](docs/PROJECT_SUMMARY.md)** - Complete project overview and implementation summary
- **[Plates Templates](docs/PLATES_TEMPLATES.md)** - Template system implementation and usage guide
- **[Path Security](docs/PATH_SECURITY.md)** - Security system documentation and best practices
- **[Quality Tools](docs/QUALITY_TOOLS.md)** - Code quality tools and automated checks

### Quick Links
- **Live Demo**: `http://localhost:8080/` - Bootstrap 5 responsive homepage
- **API Endpoints**: `/users`, `/articles`, `/security/*` - RESTful API demo
- **Quality Check**: `./bin/quality-check.sh` - Run all quality checks
- **Template System**: Native PHP templates with inheritance and components

### Implementation Status
- ✅ **Slim 4 + Doctrine 3** - Modern PHP framework with ORM
- ✅ **Path Security** - Comprehensive protection against attacks
- ✅ **Quality Tools** - PHPStan level max + PSR-12 compliance
- ✅ **Plates Templates** - Native PHP template system
- ✅ **Production Ready** - All quality checks passing

See individual documentation files for detailed implementation guides and usage examples.

## Telegram Error Logger

This project includes a Telegram Error Logger for real-time error monitoring and notifications.

### Features
- ✅ **Real-time notifications** - Instant error alerts via Telegram
- ✅ **Rich formatting** - Markdown formatting with emojis for different log levels
- ✅ **Context support** - Includes request context, user info, and custom data
- ✅ **Configurable levels** - Set minimum log level (Error, Warning, etc.)
- ✅ **Fallback logging** - Continues to log to files even if Telegram fails

### Quick Setup
1. **Create Telegram bot** with @BotFather
2. **Configure environment** variables in `.env`:
   ```env
   TELEGRAM_BOT_TOKEN=your_bot_token
   TELEGRAM_CHANNEL=your_chat_id
   TELEGRAM_APP_NAME="Your App Name"
   ```
3. **Test the setup**:
   ```bash
   curl "http://localhost:8080/test/telegram/error"
   ```

### Test Endpoints
- `/test/telegram/error` - Test error level logging
- `/test/telegram/warning` - Test warning level logging
- `/test/telegram/critical` - Test critical level logging
- `/test/telegram/exception` - Test exception logging
- `/test/telegram/context` - Test rich context logging

See `docs/TELEGRAM_LOGGER.md` for detailed setup and configuration instructions.
