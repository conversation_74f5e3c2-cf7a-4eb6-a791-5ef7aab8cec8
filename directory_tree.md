.
├── app
│   ├── routes
│   │   ├── api.php
│   │   └── web.php
│   ├── bootstrap.php
│   ├── dependencies.php
│   ├── middleware.php
│   ├── paths.php
│   ├── routes.php
│   └── settings.php
├── bin
│   ├── cs-check.sh
│   ├── cs-fix.sh
│   ├── doctrine.php
│   ├── load-article-sql-fixtures.php
│   ├── load-sql-fixtures.php
│   ├── phpstan.sh
│   ├── quality-check.sh
│   ├── seed-articles.php
│   ├── seed-users.php
│   ├── seed-users-silent.php
│   ├── setup-article-database.php
│   └── setup-database.php
├── config
├── docs
│   ├── PATH_SECURITY.md
│   ├── PLATES_TEMPLATES.md
│   ├── PROJECT_SUMMARY.md
│   └── QUALITY_TOOLS.md
├── fixtures
│   ├── ARTICLE_README.md
│   ├── articles.sql
│   ├── README.md
│   └── users.sql
├── .github
│   ├── workflows
│   │   └── tests.yml
│   └── dependabot.yml
├── logs
│   ├── app.log
│   └── README.md
├── src
│   ├── Application
│   │   ├── Actions
│   │   │   ├── Article
│   │   │   │   ├── ArticleAction.php
│   │   │   │   ├── ListArticlesAction.php
│   │   │   │   ├── ViewArticleAction.php
│   │   │   │   └── ViewArticleBySkuAction.php
│   │   │   ├── Pages
│   │   │   │   ├── ArticleDetailPageAction.php
│   │   │   │   ├── ArticlesPageAction.php
│   │   │   │   ├── HomeAction.php
│   │   │   │   ├── SecurityDemoPageAction.php
│   │   │   │   ├── UserDetailPageAction.php
│   │   │   │   └── UsersPageAction.php
│   │   │   ├── Security
│   │   │   │   ├── FileAccessAction.php
│   │   │   │   └── ListFilesAction.php
│   │   │   ├── User
│   │   │   │   ├── ListUsersAction.php
│   │   │   │   ├── UserAction.php
│   │   │   │   └── ViewUserAction.php
│   │   │   ├── ActionError.php
│   │   │   ├── ActionPayload.php
│   │   │   ├── Action.php
│   │   │   └── TemplateAction.php
│   │   ├── Handlers
│   │   │   ├── HttpErrorHandler.php
│   │   │   └── ShutdownHandler.php
│   │   ├── Helpers
│   │   │   └── SecurePathHelper.php
│   │   ├── Middleware
│   │   │   ├── PathSecurityMiddleware.php
│   │   │   └── SessionMiddleware.php
│   │   ├── ResponseEmitter
│   │   │   └── ResponseEmitter.php
│   │   └── Settings
│   │       ├── SettingsInterface.php
│   │       └── Settings.php
│   └── Domain
│       ├── Article
│       │   ├── ArticleNotFoundException.php
│       │   ├── Article.php
│       │   ├── ArticleRepository.php
│       │   └── ArticleType.php
│       ├── DomainException
│       │   ├── DomainException.php
│       │   └── DomainRecordNotFoundException.php
│       └── User
│           ├── UserNotFoundException.php
│           ├── User.php
│           └── UserRepository.php
├── templates
│   ├── articles
│   │   ├── card.php
│   │   ├── detail.php
│   │   └── index.php
│   ├── components
│   │   ├── footer.php
│   │   ├── header.php
│   │   └── navigation.php
│   ├── errors
│   │   ├── 404.php
│   │   └── error.php
│   ├── layouts
│   │   └── app.php
│   ├── pages
│   │   └── home.php
│   └── users
│       ├── card.php
│       ├── detail.php
│       └── index.php
├── tests
│   ├── Application
│   │   ├── Actions
│   │   │   ├── User
│   │   │   │   ├── ListUserActionTest.php
│   │   │   │   └── ViewUserActionTest.php
│   │   │   └── ActionTest.php
│   │   ├── Helpers
│   │   │   └── SecurePathHelperTest.php
│   │   └── Middleware
│   │       └── PathSecurityMiddlewareTest.php
│   ├── Domain
│   │   ├── Article
│   │   │   └── ArticleTest.php
│   │   └── User
│   │       └── UserTest.php
│   ├── Fixtures
│   │   ├── ArticleFixtures.php
│   │   └── UserFixtures.php
│   ├── bootstrap.php
│   └── TestCase.php
├── var
│   ├── cache
│   │   └── .gitignore
│   ├── logs
│   │   └── app.log
│   ├── storage
│   │   ├── demo
│   │   │   └── storage-file.txt
│   │   └── user.db
│   ├── article.db
│   └── user.db
├── cli-config.php
├── composer.json
├── composer.lock
├── CONTRIBUTING.md
├── .coveralls.yml
├── directory_tree.md
├── docker-compose.yml
├── .gitignore
├── .htaccess
├── LICENSE.md
├── phpcs.xml
├── phpstan-baseline.neon
├── phpstan.neon.dist
├── .phpunit.result.cache
├── phpunit.xml
└── README.md

48 directories, 114 files
