{"name": "antoninom90/slim4-doctrine-skeleton", "description": "Slim Framework 4 Skeleton Application with Doctrine ORM 2", "keywords": ["slim", "microframework", "rest", "doctrine", "router", "psr7"], "homepage": "http://github.com/AntoninoM90/slim4-doctrine-skeleton", "type": "template", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.joshlockhart.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.lgse.com/"}, {"name": "Antonino M. (@AntoninoM90)", "homepage": "https://antoninom90.altervista.org/"}], "require": {"php": "^8.2", "ext-json": "*", "doctrine/annotations": "2.*", "doctrine/dbal": "^4", "doctrine/doctrine-bundle": "^2.14", "doctrine/orm": "^3", "league/plates": "^3.6", "monolog/monolog": "^3.9", "php-di/php-di": "^7.0", "responsive-sk/slim4-paths": "^1.0", "slim/psr7": "^1.7", "slim/slim": "^4.14", "symfony/cache": "^7"}, "require-dev": {"jangregor/phpstan-prophecy": "^1.0.2", "phpspec/prophecy-phpunit": "^2.4", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^11.5.6", "squizlabs/php_codesniffer": "^3.13"}, "config": {"allow-plugins": {"phpstan/extension-installer": true}, "process-timeout": 0, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"start": "php -S localhost:8080 -t public", "test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "phpstan": "phpstan analyse --memory-limit=1G", "phpstan-baseline": "phpstan analyse --generate-baseline", "cs-check": "phpcs --standard=phpcs.xml", "cs-fix": "phpcbf --standard=phpcs.xml", "cs-check-diff": "phpcs --standard=phpcs.xml --report=diff", "quality": ["@cs-check", "@phpstan", "@test"], "quality-fix": ["@cs-fix", "@phpstan", "@test"]}}