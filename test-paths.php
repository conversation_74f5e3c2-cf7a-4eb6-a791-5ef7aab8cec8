<?php

require __DIR__ . '/vendor/autoload.php';

use ResponsiveSk\Slim4Paths\Paths;

try {
    $paths = new Paths(__DIR__);

    // Use reflection to see available methods
    $reflection = new ReflectionClass($paths);
    $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);

    echo "Available methods:\n";
    foreach ($methods as $method) {
        if (!$method->isConstructor()) {
            echo "- " . $method->getName() . "\n";

            // Try to call methods without parameters
            if ($method->getNumberOfRequiredParameters() === 0) {
                try {
                    $result = $method->invoke($paths);
                    echo "  Result: " . (is_string($result) ? $result : gettype($result)) . "\n";
                } catch (Exception $e) {
                    echo "  Error: " . $e->getMessage() . "\n";
                }
            }
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
