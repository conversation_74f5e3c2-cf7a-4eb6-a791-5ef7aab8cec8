<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->e($title ?? $appName) ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .navbar-brand { font-weight: bold; }
        .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 0; }
        .feature-card { border: 1px solid #e9ecef; border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 1rem; }
        .article-card { border: 1px solid #e9ecef; border-radius: 0.5rem; overflow: hidden; margin-bottom: 1rem; }
        .article-image { height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; }
        .placeholder-image { width: 100%; height: 100%; background: #dee2e6; display: flex; align-items: center; justify-content: center; color: #6c757d; }
        .article-meta { font-size: 0.875rem; color: #6c757d; }
        .article-meta > * { margin-right: 1rem; }
        .error-page { text-align: center; padding: 4rem 0; }
        .error-code { font-size: 6rem; font-weight: bold; color: #dc3545; margin-bottom: 0; }
        .footer { background: #f8f9fa; padding: 2rem 0; margin-top: 3rem; }
        .active { font-weight: bold; }
    </style>
    
    <?= $this->section('head') ?>
</head>
<body>
    <?= $this->insert('components::header') ?>
    
    <main>
        <?= $this->section('main') ?>
    </main>
    
    <?= $this->insert('components::footer') ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Simple navigation highlighting
        document.addEventListener('DOMContentLoaded', function() {
            const currentPage = '<?= $this->e($currentPage) ?>';
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href === '/' && currentPage === 'home') {
                    link.classList.add('active');
                } else if (href !== '/' && href.includes(currentPage)) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    <?= $this->section('scripts') ?>
</body>
</html>
