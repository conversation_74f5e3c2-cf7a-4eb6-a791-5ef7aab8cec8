<?php $this->layout('layouts::app', ['title' => $title ?? 'Welcome']) ?>

<?php $this->start('main') ?>
<div class="hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 mb-4">Welcome to <?= $this->e($appName) ?></h1>
                <p class="lead mb-4">
                    <?= $this->e($description ?? 'A modern Slim 4 application with comprehensive features') ?>
                </p>
                <div class="d-flex gap-3 justify-content-center">
                    <a href="/users" class="btn btn-light btn-lg">View Users</a>
                    <a href="/articles" class="btn btn-outline-light btn-lg">Browse Articles</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2>Application Features</h2>
            <p class="text-muted">Explore the powerful features of this Slim 4 application</p>
        </div>
    </div>
    
    <div class="row">
        <?php foreach ($features ?? [] as $feature): ?>
            <div class="col-md-4 mb-4">
                <div class="feature-card h-100">
                    <?php if (isset($feature['icon'])): ?>
                        <div class="text-center mb-3">
                            <i class="<?= $this->e($feature['icon']) ?> fa-3x text-primary"></i>
                        </div>
                    <?php endif ?>
                    
                    <h4><?= $this->e($feature['title']) ?></h4>
                    <p class="text-muted"><?= $this->e($feature['description']) ?></p>
                    
                    <?php if (isset($feature['link'])): ?>
                        <a href="<?= $this->e($feature['link']) ?>" class="btn btn-outline-primary btn-sm">
                            Learn More
                        </a>
                    <?php endif ?>
                </div>
            </div>
        <?php endforeach ?>
    </div>
</div>

<div class="bg-light py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <h3>API Endpoints</h3>
                <p class="text-muted">Test the RESTful API endpoints:</p>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>
                            <strong>GET /users</strong><br>
                            <small class="text-muted">List all users</small>
                        </span>
                        <a href="/users" class="btn btn-sm btn-outline-primary">Test</a>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>
                            <strong>GET /articles</strong><br>
                            <small class="text-muted">List all articles</small>
                        </span>
                        <a href="/articles" class="btn btn-sm btn-outline-primary">Test</a>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>
                            <strong>GET /security/file</strong><br>
                            <small class="text-muted">Secure file access demo</small>
                        </span>
                        <a href="/security/file?file=demo/safe-file.txt&dir=public" class="btn btn-sm btn-outline-primary">Test</a>
                    </li>
                </ul>
            </div>
            <div class="col-lg-6">
                <h3>Quality Metrics</h3>
                <p class="text-muted">This application maintains high code quality:</p>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center p-3 bg-white rounded">
                            <h4 class="text-success">✅</h4>
                            <small>PSR-12 Compliant</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-white rounded">
                            <h4 class="text-success">✅</h4>
                            <small>PHPStan Level Max</small>
                        </div>
                    </div>
                    <div class="col-6 mt-3">
                        <div class="text-center p-3 bg-white rounded">
                            <h4 class="text-success">✅</h4>
                            <small>Path Security</small>
                        </div>
                    </div>
                    <div class="col-6 mt-3">
                        <div class="text-center p-3 bg-white rounded">
                            <h4 class="text-success">✅</h4>
                            <small>Unit Tested</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->stop() ?>

<?php $this->start('scripts') ?>
<script>
    console.log('Homepage loaded successfully');
    
    // Add some interactivity
    document.querySelectorAll('.feature-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
<?php $this->stop() ?>
