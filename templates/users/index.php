<?php $this->layout('layouts::app', ['title' => $title]) ?>

<?php $this->start('main') ?>
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>Users</h1>
                    <p class="text-muted">Manage and view user accounts</p>
                </div>
                <div>
                    <span class="badge bg-primary fs-6"><?= $totalUsers ?> users</span>
                    <a href="<?= $this->e($apiEndpoint) ?>" class="btn btn-outline-secondary btn-sm ms-2">
                        <i class="bi bi-code"></i> API
                    </a>
                </div>
            </div>
            
            <?php if (empty($users)): ?>
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-people fa-4x text-muted"></i>
                    </div>
                    <h3 class="text-muted">No users found</h3>
                    <p class="text-muted">There are no users in the system.</p>
                    <a href="/api/v1/users" class="btn btn-primary">View API Response</a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($users as $user): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <?= $this->insert('users::card', ['user' => $user]) ?>
                        </div>
                    <?php endforeach ?>
                </div>
                
                <div class="mt-5 p-4 bg-light rounded">
                    <h5>API Information</h5>
                    <p class="mb-2">This data is also available via REST API:</p>
                    <div class="d-flex gap-2">
                        <code class="bg-white p-2 rounded">GET <?= $this->e($apiEndpoint) ?></code>
                        <a href="<?= $this->e($apiEndpoint) ?>" class="btn btn-sm btn-outline-primary">Test API</a>
                    </div>
                </div>
            <?php endif ?>
        </div>
    </div>
</div>
<?php $this->stop() ?>

<?php $this->start('scripts') ?>
<script>
    console.log('Users page loaded with <?= $totalUsers ?> users');
    
    // Add hover effects to user cards
    document.querySelectorAll('.user-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
<?php $this->stop() ?>
