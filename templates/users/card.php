<div class="card user-card h-100">
    <div class="card-body">
        <div class="d-flex align-items-center mb-3">
            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                 style="width: 50px; height: 50px; font-weight: bold;">
                <?= $this->e($user['initials']) ?>
            </div>
            <div>
                <h5 class="card-title mb-1">
                    <a href="/users/<?= $this->e($user['id']) ?>" class="text-decoration-none">
                        <?= $this->e($user['fullName']) ?>
                    </a>
                </h5>
                <p class="text-muted mb-0">@<?= $this->e($user['username']) ?></p>
            </div>
        </div>
        
        <div class="mb-3">
            <small class="text-muted">
                <i class="bi bi-envelope"></i> <?= $this->e($user['email']) ?>
            </small>
        </div>
        
        <div class="d-flex gap-2">
            <a href="/users/<?= $this->e($user['id']) ?>" class="btn btn-primary btn-sm">
                <i class="bi bi-eye"></i> View Details
            </a>
            <a href="/api/v1/users/<?= $this->e($user['id']) ?>" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-code"></i> API
            </a>
        </div>
    </div>
</div>
