<?php $this->layout('layouts::app', ['title' => $title]) ?>

<?php $this->start('main') ?>
<div class="container my-5">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <?php foreach ($breadcrumbs as $crumb): ?>
                <?php if ($crumb['url']): ?>
                    <li class="breadcrumb-item">
                        <a href="<?= $this->e($crumb['url']) ?>"><?= $this->e($crumb['name']) ?></a>
                    </li>
                <?php else: ?>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= $this->e($crumb['name']) ?>
                    </li>
                <?php endif ?>
            <?php endforeach ?>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8">
            <!-- User Profile Card -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-4" 
                             style="width: 80px; height: 80px; font-size: 2rem; font-weight: bold;">
                            <?= $this->e($user['initials']) ?>
                        </div>
                        <div>
                            <h2 class="mb-1"><?= $this->e($user['fullName']) ?></h2>
                            <p class="text-muted mb-2">@<?= $this->e($user['username']) ?></p>
                            <span class="badge bg-success">Active User</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Personal Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>First Name:</strong></td>
                                    <td><?= $this->e($user['firstName']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Last Name:</strong></td>
                                    <td><?= $this->e($user['lastName']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>
                                        <a href="mailto:<?= $this->e($user['email']) ?>">
                                            <?= $this->e($user['email']) ?>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>User ID:</strong></td>
                                    <td><code><?= $this->e($user['id']) ?></code></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Account Details</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td><code><?= $this->e($user['username']) ?></code></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td><span class="badge bg-success">Active</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Role:</strong></td>
                                    <td>User</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Actions Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/users" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> Back to Users
                        </a>
                        <a href="mailto:<?= $this->e($user['email']) ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-envelope"></i> Send Email
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- API Information Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">API Access</h6>
                </div>
                <div class="card-body">
                    <p class="small text-muted mb-3">
                        This user data is also available via REST API:
                    </p>
                    <div class="mb-3">
                        <code class="d-block bg-light p-2 rounded small">
                            GET <?= $this->e($apiEndpoint) ?>
                        </code>
                    </div>
                    <a href="<?= $this->e($apiEndpoint) ?>" class="btn btn-sm btn-outline-primary w-100">
                        <i class="bi bi-code"></i> View JSON Response
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->stop() ?>

<?php $this->start('scripts') ?>
<script>
    console.log('User detail page loaded', {
        userId: <?= $user['id'] ?>,
        username: '<?= $this->e($user['username']) ?>'
    });
</script>
<?php $this->stop() ?>
