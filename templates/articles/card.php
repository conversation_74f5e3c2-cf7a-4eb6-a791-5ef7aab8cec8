<div class="article-card h-100">
    <div class="article-image">
        <?php if (!empty($article['image'])): ?>
            <img src="<?= $this->e($article['image']) ?>" alt="<?= $this->e($article['title']) ?>" class="img-fluid">
        <?php else: ?>
            <div class="placeholder-image">
                <i class="fas fa-image fa-2x"></i>
            </div>
        <?php endif ?>
    </div>
    
    <div class="card-body">
        <h5 class="card-title">
            <a href="/article/<?= $this->e($article['id']) ?>" class="text-decoration-none">
                <?= $this->e($article['title']) ?>
            </a>
        </h5>
        
        <p class="card-text text-muted">
            <?= $this->e(substr($article['description'] ?? '', 0, 120)) ?><?= strlen($article['description'] ?? '') > 120 ? '...' : '' ?>
        </p>
        
        <div class="article-meta d-flex justify-content-between align-items-center">
            <div>
                <span class="badge bg-secondary"><?= $this->e($article['type']) ?></span>
                <?php if (!empty($article['price'])): ?>
                    <span class="badge bg-success"><?= $this->e($article['currency'] ?? '$') ?><?= $this->e($article['price']) ?></span>
                <?php endif ?>
            </div>
            
            <div class="text-end">
                <?php if (!empty($article['sku'])): ?>
                    <small class="text-muted d-block">SKU: <?= $this->e($article['sku']) ?></small>
                <?php endif ?>
                <?php if (isset($article['inStock']) && $article['inStock']): ?>
                    <small class="text-success">In Stock</small>
                <?php else: ?>
                    <small class="text-danger">Out of Stock</small>
                <?php endif ?>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="/article/<?= $this->e($article['id']) ?>" class="btn btn-primary btn-sm">
                View Details
            </a>
            <?php if (!empty($article['sku'])): ?>
                <a href="/article/sku/<?= $this->e($article['sku']) ?>" class="btn btn-outline-secondary btn-sm">
                    View by SKU
                </a>
            <?php endif ?>
        </div>
    </div>
</div>
