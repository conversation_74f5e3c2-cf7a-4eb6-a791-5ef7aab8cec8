<?php $this->layout('layouts::app', ['title' => 'Articles']) ?>

<?php $this->start('main') ?>
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Articles</h1>
                <div>
                    <span class="badge bg-primary"><?= count($articles ?? []) ?> articles</span>
                </div>
            </div>
            
            <?php if (empty($articles)): ?>
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-newspaper fa-4x text-muted"></i>
                    </div>
                    <h3 class="text-muted">No articles found</h3>
                    <p class="text-muted">There are no articles available at the moment.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($articles as $article): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <?= $this->insert('articles::card', ['article' => $article]) ?>
                        </div>
                    <?php endforeach ?>
                </div>
            <?php endif ?>
        </div>
    </div>
</div>
<?php $this->stop() ?>

<?php $this->start('scripts') ?>
<script>
    console.log('Articles page loaded with <?= count($articles ?? []) ?> articles');
</script>
<?php $this->stop() ?>
