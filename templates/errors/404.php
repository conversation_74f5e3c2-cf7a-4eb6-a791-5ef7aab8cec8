<?php $this->layout('layouts::app', ['title' => $title ?? 'Page Not Found']) ?>

<?php $this->start('main') ?>
<div class="container">
    <div class="error-page">
        <div class="row justify-content-center">
            <div class="col-lg-6 text-center">
                <h1 class="error-code">404</h1>
                <h2 class="mb-4"><?= $this->e($title ?? 'Page Not Found') ?></h2>
                <p class="lead text-muted mb-4">
                    <?= $this->e($message ?? "The page you're looking for doesn't exist.") ?>
                </p>
                
                <div class="d-flex gap-3 justify-content-center mb-5">
                    <a href="/" class="btn btn-primary">
                        <i class="bi bi-house"></i> Go Home
                    </a>
                    <a href="/articles" class="btn btn-outline-primary">
                        <i class="bi bi-journal-text"></i> Browse Articles
                    </a>
                    <a href="/users" class="btn btn-outline-secondary">
                        <i class="bi bi-people"></i> View Users
                    </a>
                </div>
                
                <div class="bg-light p-4 rounded">
                    <h5>Popular Pages</h5>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-decoration-none">Homepage</a></li>
                        <li><a href="/articles" class="text-decoration-none">Articles</a></li>
                        <li><a href="/users" class="text-decoration-none">Users</a></li>
                        <li><a href="/security/list?base=public" class="text-decoration-none">Security Demo</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->stop() ?>

<?php $this->start('scripts') ?>
<script>
    // Log 404 for analytics
    console.log('404 Error:', {
        path: '<?= $this->e($currentPath ?? '') ?>',
        timestamp: new Date().toISOString()
    });
</script>
<?php $this->stop() ?>
