<?php $this->layout('layouts::app', ['title' => $title ?? 'Error']) ?>

<?php $this->start('main') ?>
<div class="container">
    <div class="error-page">
        <div class="row justify-content-center">
            <div class="col-lg-6 text-center">
                <h1 class="error-code"><?= $this->e($statusCode ?? '500') ?></h1>
                <h2 class="mb-4"><?= $this->e($title ?? 'An Error Occurred') ?></h2>
                <p class="lead text-muted mb-4">
                    <?= $this->e($message ?? 'Something went wrong. Please try again later.') ?>
                </p>
                
                <div class="d-flex gap-3 justify-content-center mb-5">
                    <a href="/" class="btn btn-primary">
                        <i class="bi bi-house"></i> Go Home
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> Go Back
                    </button>
                    <button onclick="location.reload()" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i> Retry
                    </button>
                </div>
                
                <?php if (isset($details) && !empty($details)): ?>
                    <div class="bg-light p-4 rounded text-start">
                        <h6>Error Details</h6>
                        <small class="text-muted">
                            <?= $this->e($details) ?>
                        </small>
                    </div>
                <?php endif ?>
            </div>
        </div>
    </div>
</div>
<?php $this->stop() ?>

<?php $this->start('scripts') ?>
<script>
    // Log error for debugging
    console.error('Application Error:', {
        statusCode: <?= $statusCode ?? 500 ?>,
        message: '<?= $this->e($message ?? '') ?>',
        path: '<?= $this->e($currentPath ?? '') ?>',
        timestamp: new Date().toISOString()
    });
</script>
<?php $this->stop() ?>
