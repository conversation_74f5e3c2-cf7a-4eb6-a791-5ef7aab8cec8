Options All -Indexes

# Protect .htaccess and .htpasswd files.
<Files ".ht*">
    Require all denied
</Files>

<IfModule mod_rewrite.c>
  # Redirect to the public folder
  RewriteEngine On
  # RewriteBase /
  RewriteRule ^$ public/ [L]
  RewriteRule (.*) public/$1 [L]

  # Redirect to HTTPS
  # RewriteEngine On
  # RewriteCond %{HTTPS} off
  # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>
