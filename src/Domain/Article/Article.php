<?php

declare(strict_types=1);

namespace App\Domain\Article;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;
use JsonSerializable;
use DateTimeImmutable;

#[
    Entity(),
    Table(name: "article")
]
class Article implements JsonSerializable
{
    #[Id, Column(
        name: 'id',
        type: 'integer',
        length: 18,
        unique: true,
        nullable: false
    ), GeneratedValue('IDENTITY')]
    private ?int $id;

    #[Column(
        name: 'title',
        type: 'string',
        length: 255,
        nullable: false
    )]
    private string $title;

    #[Column(
        name: 'description',
        type: 'text',
        nullable: true
    )]
    private ?string $description;

    #[Column(
        name: 'type',
        type: 'string',
        length: 50,
        nullable: false,
        enumType: ArticleType::class
    )]
    private ArticleType $type;

    #[Column(
        name: 'sku',
        type: 'string',
        length: 100,
        unique: true,
        nullable: false
    )]
    private string $sku;

    #[Column(
        name: 'price',
        type: 'decimal',
        precision: 10,
        scale: 2,
        nullable: true
    )]
    private ?float $price;

    #[Column(
        name: 'currency',
        type: 'string',
        length: 3,
        nullable: false
    )]
    private string $currency = 'EUR';

    #[Column(
        name: 'delivery_method',
        type: 'string',
        length: 50,
        nullable: true
    )]
    private ?string $deliveryMethod;

    #[Column(
        name: 'delivery_time_days',
        type: 'integer',
        nullable: true
    )]
    private ?int $deliveryTimeDays;

    #[Column(
        name: 'stock_quantity',
        type: 'integer',
        nullable: false
    )]
    private int $stockQuantity = 0;

    #[Column(
        name: 'is_active',
        type: 'boolean',
        nullable: false
    )]
    private bool $isActive = true;

    #[Column(
        name: 'created_at',
        type: 'datetime_immutable',
        nullable: false
    )]
    private DateTimeImmutable $createdAt;

    #[Column(
        name: 'updated_at',
        type: 'datetime_immutable',
        nullable: false
    )]
    private DateTimeImmutable $updatedAt;

    public function __construct(
        ?int $id,
        string $title,
        ArticleType $type,
        string $sku,
        ?string $description = null,
        ?float $price = null,
        string $currency = 'EUR',
        ?string $deliveryMethod = null,
        ?int $deliveryTimeDays = null,
        int $stockQuantity = 0,
        bool $isActive = true
    ) {
        $this->id = $id;
        $this->title = $title;
        $this->type = $type;
        $this->sku = strtoupper($sku);
        $this->description = $description;
        $this->price = $price;
        $this->currency = strtoupper($currency);
        $this->deliveryMethod = $deliveryMethod;
        $this->deliveryTimeDays = $deliveryTimeDays;
        $this->stockQuantity = $stockQuantity;
        $this->isActive = $isActive;
        $this->createdAt = new DateTimeImmutable();
        $this->updatedAt = new DateTimeImmutable();
    }

    // Getters
    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getType(): ArticleType
    {
        return $this->type;
    }

    public function getSku(): string
    {
        return $this->sku;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function getDeliveryMethod(): ?string
    {
        return $this->deliveryMethod;
    }

    public function getDeliveryTimeDays(): ?int
    {
        return $this->deliveryTimeDays;
    }

    public function getStockQuantity(): int
    {
        return $this->stockQuantity;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    // Setters
    public function setTitle(string $title): self
    {
        $this->title = $title;
        $this->updateTimestamp();
        return $this;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        $this->updateTimestamp();
        return $this;
    }

    public function setType(ArticleType $type): self
    {
        $this->type = $type;
        $this->updateTimestamp();
        return $this;
    }

    public function setSku(string $sku): self
    {
        $this->sku = strtoupper($sku);
        $this->updateTimestamp();
        return $this;
    }

    public function setPrice(?float $price): self
    {
        $this->price = $price;
        $this->updateTimestamp();
        return $this;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = strtoupper($currency);
        $this->updateTimestamp();
        return $this;
    }

    public function setDeliveryMethod(?string $deliveryMethod): self
    {
        $this->deliveryMethod = $deliveryMethod;
        $this->updateTimestamp();
        return $this;
    }

    public function setDeliveryTimeDays(?int $deliveryTimeDays): self
    {
        $this->deliveryTimeDays = $deliveryTimeDays;
        $this->updateTimestamp();
        return $this;
    }

    public function setStockQuantity(int $stockQuantity): self
    {
        $this->stockQuantity = $stockQuantity;
        $this->updateTimestamp();
        return $this;
    }

    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;
        $this->updateTimestamp();
        return $this;
    }

    // Helper methods
    private function updateTimestamp(): void
    {
        $this->updatedAt = new DateTimeImmutable();
    }

    public function isInStock(): bool
    {
        return $this->stockQuantity > 0;
    }

    public function requiresDelivery(): bool
    {
        return $this->type->requiresPhysicalDelivery();
    }

    public function getFormattedPrice(): string
    {
        if ($this->price === null) {
            return 'Free';
        }
        return number_format($this->price, 2) . ' ' . $this->currency;
    }

    #[\ReturnTypeWillChange]
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type->value,
            'typeLabel' => $this->type->getLabel(),
            'sku' => $this->sku,
            'price' => $this->price,
            'currency' => $this->currency,
            'formattedPrice' => $this->getFormattedPrice(),
            'deliveryMethod' => $this->deliveryMethod,
            'deliveryTimeDays' => $this->deliveryTimeDays,
            'stockQuantity' => $this->stockQuantity,
            'isActive' => $this->isActive,
            'isInStock' => $this->isInStock(),
            'requiresDelivery' => $this->requiresDelivery(),
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt->format('Y-m-d H:i:s'),
        ];
    }
}
