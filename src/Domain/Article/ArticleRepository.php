<?php

declare(strict_types=1);

namespace App\Domain\Article;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;

class ArticleRepository
{
    protected EntityManagerInterface $entityManager;

    protected EntityRepository $repository;

    public function __construct(
        EntityManagerInterface $entityManager
    ) {
        $this->entityManager = $entityManager;
        $this->repository = $this->entityManager->getRepository(Article::class);
    }

    /**
     * @return Article[]
     */
    public function findAllArticles(): array
    {
        return $this->repository->findBy(['isActive' => true], ['createdAt' => 'DESC']);
    }

    /**
     * @return Article[]
     */
    public function findAllArticlesIncludingInactive(): array
    {
        return $this->repository->findBy([], ['createdAt' => 'DESC']);
    }

    /**
     * @param int $id
     * @param ?int $lockMode
     * @param ?int $lockVersion
     *
     * @return Article|null
     * @throws ArticleNotFoundException
     */
    public function findArticleOfId(
        int $id,
        ?int $lockMode = null,
        ?int $lockVersion = null
    ): ?Article {
        return $this->repository->find($id, $lockMode, $lockVersion);
    }

    /**
     * @param string $sku
     * @return Article|null
     */
    public function findArticleBySku(string $sku): ?Article
    {
        return $this->repository->findOneBy(['sku' => strtoupper($sku)]);
    }

    /**
     * @param ArticleType $type
     * @return Article[]
     */
    public function findArticlesByType(ArticleType $type): array
    {
        return $this->repository->findBy([
            'type' => $type,
            'isActive' => true
        ], ['createdAt' => 'DESC']);
    }

    /**
     * @return Article[]
     */
    public function findInStockArticles(): array
    {
        return $this->repository->createQueryBuilder('a')
            ->where('a.isActive = :active')
            ->andWhere('a.stockQuantity > 0')
            ->setParameter('active', true)
            ->orderBy('a.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param float $minPrice
     * @param float $maxPrice
     * @return Article[]
     */
    public function findArticlesByPriceRange(float $minPrice, float $maxPrice): array
    {
        return $this->repository->createQueryBuilder('a')
            ->where('a.isActive = :active')
            ->andWhere('a.price BETWEEN :minPrice AND :maxPrice')
            ->setParameter('active', true)
            ->setParameter('minPrice', $minPrice)
            ->setParameter('maxPrice', $maxPrice)
            ->orderBy('a.price', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $searchTerm
     * @return Article[]
     */
    public function searchArticles(string $searchTerm): array
    {
        return $this->repository->createQueryBuilder('a')
            ->where('a.isActive = :active')
            ->andWhere('a.title LIKE :searchTerm OR a.description LIKE :searchTerm OR a.sku LIKE :searchTerm')
            ->setParameter('active', true)
            ->setParameter('searchTerm', '%' . $searchTerm . '%')
            ->orderBy('a.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param array $criteria
     * @param array|null $orderBy
     * @param mixed $limit
     * @param mixed $offset
     *
     * @return Article[]
     */
    public function findArticlesBy(
        array $criteria,
        ?array $orderBy = null,
        $limit = null,
        $offset = null
    ): array {
        return $this->repository->findBy($criteria, $orderBy, $limit, $offset);
    }

    /**
     * @param array $criteria
     * @param array|null $orderBy
     *
     * @return Article|null
     */
    public function findOneArticleBy(
        array $criteria,
        ?array $orderBy = null
    ): ?Article {
        return $this->repository->findOneBy($criteria, $orderBy);
    }

    /**
     * Save article to database
     */
    public function save(Article $article): void
    {
        $this->entityManager->persist($article);
        $this->entityManager->flush();
    }

    /**
     * Delete article from database
     */
    public function delete(Article $article): void
    {
        $this->entityManager->remove($article);
        $this->entityManager->flush();
    }

    /**
     * Get total count of active articles
     */
    public function getTotalActiveCount(): int
    {
        return (int) $this->repository->createQueryBuilder('a')
            ->select('COUNT(a.id)')
            ->where('a.isActive = :active')
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Get articles with pagination
     * @param int $page
     * @param int $limit
     * @return Article[]
     */
    public function findArticlesPaginated(int $page = 1, int $limit = 10): array
    {
        $offset = ($page - 1) * $limit;
        
        return $this->repository->createQueryBuilder('a')
            ->where('a.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('a.createdAt', 'DESC')
            ->setFirstResult($offset)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}
