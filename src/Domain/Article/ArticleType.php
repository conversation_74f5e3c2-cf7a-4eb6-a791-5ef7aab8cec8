<?php

declare(strict_types=1);

namespace App\Domain\Article;

/**
 * Article type enumeration
 * Defines different types of articles available in the system
 */
enum ArticleType: string
{
    case BLOG_ART = 'blog_art';
    case PHYSICAL_ART = 'physical_art';
    case DIGITAL_ART = 'digital_art';
    case PHOTOGRAPHY = 'photography';
    case SCULPTURE = 'sculpture';
    case PAINTING = 'painting';
    case DRAWING = 'drawing';
    case MIXED_MEDIA = 'mixed_media';
    case COLLECTIBLE = 'collectible';
    case VINTAGE = 'vintage';

    /**
     * Get human-readable label for the article type
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::BLOG_ART => 'Blog Article',
            self::PHYSICAL_ART => 'Physical Art',
            self::DIGITAL_ART => 'Digital Art',
            self::PHOTOGRAPHY => 'Photography',
            self::SCULPTURE => 'Sculpture',
            self::PAINTING => 'Painting',
            self::DRAWING => 'Drawing',
            self::MIXED_MEDIA => 'Mixed Media',
            self::COLLECTIBLE => 'Collectible',
            self::VINTAGE => 'Vintage',
        };
    }

    /**
     * Get description for the article type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::BLOG_ART => 'Written articles and blog posts about art',
            self::PHYSICAL_ART => 'Physical artwork that can be shipped',
            self::DIGITAL_ART => 'Digital artwork delivered electronically',
            self::PHOTOGRAPHY => 'Photographic artwork and prints',
            self::SCULPTURE => 'Three-dimensional sculptural works',
            self::PAINTING => 'Painted artwork on various mediums',
            self::DRAWING => 'Hand-drawn artwork and sketches',
            self::MIXED_MEDIA => 'Artwork combining multiple mediums',
            self::COLLECTIBLE => 'Collectible art pieces and limited editions',
            self::VINTAGE => 'Vintage and antique art pieces',
        };
    }

    /**
     * Check if this article type requires physical delivery
     */
    public function requiresPhysicalDelivery(): bool
    {
        return match ($this) {
            self::PHYSICAL_ART,
            self::SCULPTURE,
            self::PAINTING,
            self::DRAWING,
            self::MIXED_MEDIA,
            self::COLLECTIBLE,
            self::VINTAGE => true,
            default => false,
        };
    }

    /**
     * Check if this article type is digital content
     */
    public function isDigitalContent(): bool
    {
        return match ($this) {
            self::BLOG_ART,
            self::DIGITAL_ART => true,
            default => false,
        };
    }

    /**
     * Get all available article types
     * @return ArticleType[]
     */
    public static function getAllTypes(): array
    {
        return self::cases();
    }

    /**
     * Get all physical article types
     * @return ArticleType[]
     */
    public static function getPhysicalTypes(): array
    {
        return array_filter(self::cases(), fn($type) => $type->requiresPhysicalDelivery());
    }

    /**
     * Get all digital article types
     * @return ArticleType[]
     */
    public static function getDigitalTypes(): array
    {
        return array_filter(self::cases(), fn($type) => $type->isDigitalContent());
    }
}
