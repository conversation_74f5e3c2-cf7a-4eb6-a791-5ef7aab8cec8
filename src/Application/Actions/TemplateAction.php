<?php

declare(strict_types=1);

namespace App\Application\Actions;

use League\Plates\Engine;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;

/**
 * Base Template Action
 *
 * Provides template rendering capabilities for actions that need to return HTML
 */
abstract class TemplateAction extends Action
{
    protected Engine $templates;

    /** @var array<array{type: string, message: string}> */
    protected array $flashMessages = [];

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        /** @var Engine $templates */
        $templates = $container->get(Engine::class);
        $this->templates = $templates;
    }

    /**
     * Render template with data
     */
    protected function render(string $template, array $data = []): Response
    {
        // Add global template variables
        $data = array_merge($this->getGlobalData(), $data);

        $html = $this->templates->render($template, $data);

        $this->response->getBody()->write($html);
        return $this->response->withHeader('Content-Type', 'text/html; charset=utf-8');
    }

    /**
     * Get global template data available to all templates
     */
    protected function getGlobalData(): array
    {
        return [
            'appName' => 'Slim 4 App',
            'currentPage' => $this->getCurrentPage(),
            'currentPath' => $this->request->getUri()->getPath(),
            'queryParams' => $this->request->getQueryParams(),
            'method' => $this->request->getMethod(),
            'timestamp' => time(),
        ];
    }

    /**
     * Get current page identifier for navigation highlighting
     */
    protected function getCurrentPage(): string
    {
        $path = $this->request->getUri()->getPath();

        if ($path === '/') {
            return 'home';
        }

        $segments = explode('/', trim($path, '/'));
        return $segments[0] ?? 'unknown';
    }

    /**
     * Render error page
     */
    protected function renderError(int $statusCode, string $message = '', array $data = []): Response
    {
        $errorData = array_merge([
            'statusCode' => $statusCode,
            'message' => $message ?: $this->getDefaultErrorMessage($statusCode),
            'title' => $this->getErrorTitle($statusCode),
        ], $data);

        $template = $this->getErrorTemplate($statusCode);

        return $this->render($template, $errorData)
            ->withStatus($statusCode);
    }

    /**
     * Get error template name based on status code
     */
    protected function getErrorTemplate(int $statusCode): string
    {
        $specificTemplate = "errors::{$statusCode}";

        // Check if specific error template exists
        if ($this->templates->exists($specificTemplate)) {
            return $specificTemplate;
        }

        // Fall back to generic error template
        return 'errors::error';
    }

    /**
     * Get default error message for status code
     */
    protected function getDefaultErrorMessage(int $statusCode): string
    {
        return match ($statusCode) {
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Page Not Found',
            405 => 'Method Not Allowed',
            500 => 'Internal Server Error',
            503 => 'Service Unavailable',
            default => 'An error occurred',
        };
    }

    /**
     * Get error page title
     */
    protected function getErrorTitle(int $statusCode): string
    {
        return match ($statusCode) {
            400 => 'Bad Request',
            401 => 'Unauthorized Access',
            403 => 'Access Forbidden',
            404 => 'Page Not Found',
            405 => 'Method Not Allowed',
            500 => 'Server Error',
            503 => 'Service Unavailable',
            default => 'Error',
        };
    }

    /**
     * Add flash message to template data
     */
    protected function addFlashMessage(string $type, string $message): void
    {
        // This would integrate with session flash messages
        // For now, we'll store in a simple array
        $this->flashMessages[] = [
            'type' => $type,
            'message' => $message,
        ];
    }

    /**
     * Get flash messages for templates
     */
    protected function getFlashMessages(): array
    {
        return $this->flashMessages;
    }

    /**
     * Render JSON response (for AJAX requests)
     */
    protected function renderJson(array $data, int $statusCode = 200): Response
    {
        $payload = json_encode($data, JSON_PRETTY_PRINT);

        if ($payload === false) {
            $payload = '{"error": "JSON encoding failed"}';
        }

        $this->response->getBody()->write($payload);
        return $this->response
            ->withHeader('Content-Type', 'application/json')
            ->withStatus($statusCode);
    }

    /**
     * Check if request expects JSON response
     */
    protected function expectsJson(): bool
    {
        $accept = $this->request->getHeaderLine('Accept');
        $contentType = $this->request->getHeaderLine('Content-Type');

        return str_contains($accept, 'application/json') ||
               str_contains($contentType, 'application/json') ||
               $this->request->hasHeader('X-Requested-With');
    }

    /**
     * Render response based on request type (HTML or JSON)
     */
    protected function renderResponse(string $template, array $data = [], int $statusCode = 200): Response
    {
        if ($this->expectsJson()) {
            return $this->renderJson($data, $statusCode);
        }

        return $this->render($template, $data)->withStatus($statusCode);
    }
}
