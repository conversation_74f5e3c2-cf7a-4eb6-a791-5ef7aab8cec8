<?php

declare(strict_types=1);

namespace App\Application\Actions\Security;

use App\Application\Actions\Action;
use App\Application\Helpers\SecurePathHelper;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpBadRequestException;
use Slim\Exception\HttpNotFoundException;

/**
 * File Access Action
 *
 * Demonstrates secure file access using SecurePathHelper
 * This action shows how to safely handle file paths from user input
 */
class FileAccessAction extends Action
{
    private SecurePathHelper $securePathHelper;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        $this->securePathHelper = $container->get(SecurePathHelper::class);
    }

    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        // Get file path from query parameters
        $queryParams = $this->request->getQueryParams();
        $filePath = $queryParams['file'] ?? '';
        $directory = $queryParams['dir'] ?? 'public';

        if (empty($filePath)) {
            throw new HttpBadRequestException($this->request, 'File parameter is required');
        }

        try {
            // Use SecurePathHelper to safely access the file
            $securePath = $this->securePathHelper->securePath($filePath, $directory);
            
            // Check if file exists
            if (!$this->securePathHelper->fileExists($filePath, $directory)) {
                throw new HttpNotFoundException($this->request, "File '{$filePath}' not found");
            }

            // Get file information
            $fileInfo = [
                'requested_path' => $filePath,
                'directory' => $directory,
                'secure_path' => $securePath,
                'exists' => true,
                'readable' => is_readable($securePath),
                'size' => filesize($securePath),
                'modified' => date('Y-m-d H:i:s', filemtime($securePath)),
                'mime_type' => mime_content_type($securePath) ?: 'unknown',
            ];

            // If it's a text file, include content preview
            if (str_starts_with($fileInfo['mime_type'], 'text/') && $fileInfo['size'] < 10240) {
                try {
                    $content = $this->securePathHelper->readFile($filePath, $directory);
                    $fileInfo['content_preview'] = substr($content, 0, 500);
                    if (strlen($content) > 500) {
                        $fileInfo['content_preview'] .= '... (truncated)';
                    }
                } catch (\Exception $e) {
                    $fileInfo['content_preview'] = 'Unable to read content: ' . $e->getMessage();
                }
            }

            $this->logger->info('Secure file access', [
                'file' => $filePath,
                'directory' => $directory,
                'secure_path' => $securePath
            ]);

            return $this->respondWithData($fileInfo);

        } catch (\InvalidArgumentException $e) {
            // Log security violation attempt
            $this->logger->warning('Path security violation attempt', [
                'file' => $filePath,
                'directory' => $directory,
                'error' => $e->getMessage(),
                'ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown'
            ]);

            throw new HttpBadRequestException($this->request, 'Security violation: ' . $e->getMessage());
        }
    }
}
