<?php

declare(strict_types=1);

namespace App\Application\Actions\Security;

use App\Application\Actions\Action;
use App\Application\Helpers\SecurePathHelper;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpBadRequestException;

/**
 * List Files Action
 *
 * Demonstrates secure directory listing using SecurePathHelper
 */
class ListFilesAction extends Action
{
    private SecurePathHelper $securePathHelper;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        $this->securePathHelper = $container->get(SecurePathHelper::class);
    }

    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        // Get directory path from query parameters
        $queryParams = $this->request->getQueryParams();
        $directoryPath = $queryParams['path'] ?? '';
        $baseDirectory = $queryParams['base'] ?? 'public';

        try {
            // Use SecurePathHelper to safely list directory contents
            $files = $this->securePathHelper->listFiles($directoryPath, $baseDirectory);
            
            $response = [
                'directory' => $directoryPath ?: '/',
                'base_directory' => $baseDirectory,
                'file_count' => count($files),
                'files' => $files,
                'allowed_directories' => array_keys($this->securePathHelper->getAllowedDirectories())
            ];

            $this->logger->info('Secure directory listing', [
                'path' => $directoryPath,
                'base' => $baseDirectory,
                'file_count' => count($files)
            ]);

            return $this->respondWithData($response);

        } catch (\InvalidArgumentException $e) {
            // Log security violation attempt
            $this->logger->warning('Directory listing security violation', [
                'path' => $directoryPath,
                'base' => $baseDirectory,
                'error' => $e->getMessage(),
                'ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown'
            ]);

            throw new HttpBadRequestException($this->request, 'Security violation: ' . $e->getMessage());
        }
    }
}
