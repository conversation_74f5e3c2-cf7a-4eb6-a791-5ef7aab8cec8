<?php

declare(strict_types=1);

namespace App\Application\Actions\Pages;

use App\Application\Actions\TemplateAction;
use Psr\Http\Message\ResponseInterface as Response;

/**
 * Home Page Action
 *
 * Renders the application homepage using Plates templates
 */
class HomeAction extends TemplateAction
{
    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        $data = [
            'title' => 'Welcome to Slim 4 App',
            'description' => 'A modern PHP application built with Slim 4, Doctrine 3, ' .
                'path security, quality tools, and Plates templates.',
            'features' => [
                [
                    'title' => 'Slim 4 Framework',
                    'description' => 'Fast, modern PHP micro-framework for APIs and web applications.',
                    'icon' => 'fas fa-rocket',
                    'link' => 'https://www.slimframework.com/',
                ],
                [
                    'title' => 'Doctrine 3 ORM',
                    'description' => 'Powerful database abstraction layer with separate databases for different domains.',
                    'icon' => 'fas fa-database',
                    'link' => '/users',
                ],
                [
                    'title' => 'Path Security',
                    'description' => 'Comprehensive protection against path traversal attacks with realpath validation.',
                    'icon' => 'fas fa-shield-alt',
                    'link' => '/security/list?base=public',
                ],
                [
                    'title' => 'Quality Tools',
                    'description' => 'PHPStan level max, PSR-12 compliance, and automated quality checks.',
                    'icon' => 'fas fa-check-circle',
                    'link' => '#',
                ],
                [
                    'title' => 'Plates Templates',
                    'description' => 'Native PHP template engine with inheritance, components, and automatic escaping.',
                    'icon' => 'fas fa-code',
                    'link' => '#',
                ],
                [
                    'title' => 'Modern PHP',
                    'description' => 'PHP 8.2+ features, strict types, and modern development practices.',
                    'icon' => 'fab fa-php',
                    'link' => '#',
                ],
            ],
        ];

        $this->logger->info('Homepage accessed', [
            'ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $this->request->getHeaderLine('User-Agent'),
        ]);

        return $this->render('pages::home', $data);
    }
}
