<?php

declare(strict_types=1);

namespace App\Application\Actions\Pages;

use App\Application\Actions\TemplateAction;
use App\Domain\Article\ArticleRepository;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;

/**
 * Articles Page Action
 *
 * Renders the articles listing page using Plates templates
 */
class ArticlesPageAction extends TemplateAction
{
    private ArticleRepository $articleRepository;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        $this->articleRepository = new ArticleRepository($this->articleEntityManager);
    }

    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        $articles = $this->articleRepository->findAllArticles();

        // Transform articles for template display
        $articlesData = array_map(function ($article) {
            return [
                'id' => $article->getId(),
                'title' => $article->getTitle(),
                'description' => $article->getDescription(),
                'type' => $article->getType()->value,
                'typeLabel' => ucfirst($article->getType()->value),
                'price' => $article->getPrice(),
                'formattedPrice' => $article->getPrice() ? '$' . number_format($article->getPrice(), 2) : null,
                'currency' => '$',
                'sku' => $article->getSku(),
                'inStock' => $article->getPrice() > 0, // Simple stock logic
                'createdAt' => $article->getCreatedAt()?->format('Y-m-d H:i:s'),
                'image' => null, // Placeholder for future image functionality
            ];
        }, $articles);

        // Group articles by type for better display
        $articlesByType = [];
        foreach ($articlesData as $article) {
            $articlesByType[$article['type']][] = $article;
        }

        $data = [
            'title' => 'Articles',
            'articles' => $articlesData,
            'articlesByType' => $articlesByType,
            'totalArticles' => count($articlesData),
            'types' => array_keys($articlesByType),
            'apiEndpoint' => '/api/v1/articles',
        ];

        $this->logger->info('Articles page accessed', [
            'article_count' => count($articlesData),
            'types' => array_keys($articlesByType),
            'ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
        ]);

        return $this->render('articles::index', $data);
    }
}
