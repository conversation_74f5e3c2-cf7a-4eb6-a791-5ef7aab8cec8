<?php

declare(strict_types=1);

namespace App\Application\Actions\Pages;

use App\Application\Actions\TemplateAction;
use App\Application\Helpers\SecurePathHelper;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;

/**
 * Security Demo Page Action
 *
 * Renders the security demonstration page using Plates templates
 */
class SecurityDemoPageAction extends TemplateAction
{
    private SecurePathHelper $securePathHelper;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        /** @var SecurePathHelper $securePathHelper */
        $securePathHelper = $container->get(SecurePathHelper::class);
        $this->securePathHelper = $securePathHelper;
    }

    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        $queryParams = $this->request->getQueryParams();
        $file = $queryParams['file'] ?? '';
        $directory = $queryParams['dir'] ?? 'public';

        $demoData = [
            'allowedDirectories' => array_keys($this->securePathHelper->getAllowedDirectories()),
            'blockedPatterns' => [
                '../', '..\\', '..%2f', '..%2F', '..%5c', '..%5C',
                '%2e%2e%2f', '%2e%2e%5c', '..../', '....\\',
            ],
            'safeExamples' => [
                ['file' => 'demo/safe-file.txt', 'dir' => 'public', 'description' => 'Safe file in public directory'],
                ['file' => 'demo/storage-file.txt', 'dir' => 'storage', 'description' => 'Safe file in storage directory'],
            ],
            'dangerousExamples' => [
                ['file' => '../../../etc/passwd', 'dir' => 'public', 'description' => 'Path traversal attempt (Unix)'],
                ['file' => '..\\..\\..\\windows\\system32\\config\\sam', 'dir' => 'public', 'description' => 'Path traversal attempt (Windows)'],
                ['file' => '..%2f..%2f..%2fetc%2fpasswd', 'dir' => 'public', 'description' => 'URL encoded path traversal'],
            ],
        ];

        $fileResult = null;
        $error = null;

        // If file parameter is provided, try to access it
        if (!empty($file)) {
            try {
                if ($this->securePathHelper->fileExists($file, $directory)) {
                    $securePath = $this->securePathHelper->securePath($file, $directory);
                    $fileResult = [
                        'requested_path' => $file,
                        'directory' => $directory,
                        'secure_path' => $securePath,
                        'exists' => true,
                        'readable' => is_readable($securePath),
                        'size' => filesize($securePath),
                        'modified' => date('Y-m-d H:i:s', (int) filemtime($securePath)),
                        'mime_type' => mime_content_type($securePath) ?: 'unknown',
                        'status' => 'success',
                    ];

                    // If it's a text file, include content preview
                    if (str_starts_with($fileResult['mime_type'], 'text/') && $fileResult['size'] < 10240) {
                        $content = $this->securePathHelper->readFile($file, $directory);
                        $fileResult['content_preview'] = substr($content, 0, 500);
                        if (strlen($content) > 500) {
                            $fileResult['content_preview'] .= '... (truncated)';
                        }
                    }
                } else {
                    $fileResult = [
                        'requested_path' => $file,
                        'directory' => $directory,
                        'exists' => false,
                        'status' => 'not_found',
                    ];
                }
            } catch (\InvalidArgumentException $e) {
                $error = [
                    'type' => 'security_violation',
                    'message' => $e->getMessage(),
                    'requested_path' => $file,
                    'directory' => $directory,
                ];

                $this->logger->warning('Security demo violation attempt', [
                    'file' => $file,
                    'directory' => $directory,
                    'error' => $e->getMessage(),
                    'ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
                ]);
            }
        }

        $data = [
            'title' => 'Security Demo - Path Traversal Protection',
            'demo' => $demoData,
            'fileResult' => $fileResult,
            'error' => $error,
            'currentFile' => $file,
            'currentDirectory' => $directory,
            'breadcrumbs' => [
                ['name' => 'Home', 'url' => '/'],
                ['name' => 'Security Demo', 'url' => null],
            ],
        ];

        return $this->render('pages::security-demo', $data);
    }
}
