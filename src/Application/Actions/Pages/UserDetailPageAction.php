<?php

declare(strict_types=1);

namespace App\Application\Actions\Pages;

use App\Application\Actions\TemplateAction;
use App\Domain\User\UserRepository;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpNotFoundException;

/**
 * User Detail Page Action
 *
 * Renders a single user detail page using Plates templates
 */
class UserDetailPageAction extends TemplateAction
{
    private UserRepository $userRepository;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        $this->userRepository = new UserRepository($this->entityManager);
    }

    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        $userId = (int) $this->resolveArg('id');
        $user = $this->userRepository->findUserOfId($userId);

        if (!$user) {
            throw new HttpNotFoundException($this->request, "User with ID {$userId} not found");
        }

        // Transform user for template display
        $userData = [
            'id' => $user->getId(),
            'username' => $user->getUsername(),
            'firstName' => $user->getFirstName(),
            'lastName' => $user->getLastName(),
            'email' => $user->getUsername() . '@example.com', // Mock email
            'fullName' => $user->getFirstName() . ' ' . $user->getLastName(),
            'initials' => substr($user->getFirstName(), 0, 1) . substr($user->getLastName(), 0, 1),
        ];

        $data = [
            'title' => 'User: ' . $userData['fullName'],
            'user' => $userData,
            'breadcrumbs' => [
                ['name' => 'Home', 'url' => '/'],
                ['name' => 'Users', 'url' => '/users'],
                ['name' => $userData['fullName'], 'url' => null],
            ],
            'apiEndpoint' => "/api/v1/users/{$userId}",
        ];

        $this->logger->info('User detail page accessed', [
            'user_id' => $userId,
            'username' => $user->getUsername(),
            'ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
        ]);

        return $this->render('users::detail', $data);
    }
}
