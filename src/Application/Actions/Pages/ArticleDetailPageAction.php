<?php

declare(strict_types=1);

namespace App\Application\Actions\Pages;

use App\Application\Actions\TemplateAction;
use App\Domain\Article\ArticleRepository;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpNotFoundException;

/**
 * Article Detail Page Action
 *
 * Renders a single article detail page using Plates templates
 */
class ArticleDetailPageAction extends TemplateAction
{
    private ArticleRepository $articleRepository;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        $this->articleRepository = new ArticleRepository($this->articleEntityManager);
    }

    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        // Check if we're accessing by ID or SKU
        $articleId = $this->resolveArg('id');
        $sku = $this->resolveArg('sku');

        if ($articleId) {
            $article = $this->articleRepository->findArticleOfId((int) $articleId);
            $identifier = "ID {$articleId}";
        } elseif ($sku) {
            $article = $this->articleRepository->findArticleBySku($sku);
            $identifier = "SKU {$sku}";
        } else {
            throw new HttpNotFoundException($this->request, 'Article identifier not provided');
        }

        if (!$article) {
            throw new HttpNotFoundException($this->request, "Article with {$identifier} not found");
        }

        // Transform article for template display
        $articleData = [
            'id' => $article->getId(),
            'title' => $article->getTitle(),
            'description' => $article->getDescription(),
            'type' => $article->getType()->value,
            'typeLabel' => ucfirst($article->getType()->value),
            'price' => $article->getPrice(),
            'formattedPrice' => $article->getPrice() ? '$' . number_format($article->getPrice(), 2) : 'Free',
            'currency' => '$',
            'sku' => $article->getSku(),
            'inStock' => $article->getPrice() > 0, // Simple stock logic
            'createdAt' => $article->getCreatedAt()?->format('Y-m-d H:i:s'),
            'formattedDate' => $article->getCreatedAt()?->format('F j, Y'),
            'image' => null, // Placeholder for future image functionality
        ];

        $data = [
            'title' => 'Article: ' . $articleData['title'],
            'article' => $articleData,
            'breadcrumbs' => [
                ['name' => 'Home', 'url' => '/'],
                ['name' => 'Articles', 'url' => '/articles'],
                ['name' => $articleData['title'], 'url' => null],
            ],
            'apiEndpoint' => "/api/v1/articles/{$article->getId()}",
            'relatedArticles' => $this->getRelatedArticles($article),
        ];

        $this->logger->info('Article detail page accessed', [
            'article_id' => $article->getId(),
            'sku' => $article->getSku(),
            'title' => $article->getTitle(),
            'access_method' => $articleId ? 'id' : 'sku',
            'ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
        ]);

        return $this->render('articles::detail', $data);
    }

    /**
     * Get related articles (same type, excluding current)
     */
    private function getRelatedArticles($currentArticle): array
    {
        $allArticles = $this->articleRepository->findAllArticles();
        $relatedArticles = [];

        foreach ($allArticles as $article) {
            if (
                $article->getId() !== $currentArticle->getId() &&
                $article->getType() === $currentArticle->getType()
            ) {
                $relatedArticles[] = [
                    'id' => $article->getId(),
                    'title' => $article->getTitle(),
                    'description' => substr($article->getDescription() ?? '', 0, 100) . '...',
                    'type' => $article->getType()->value,
                    'price' => $article->getPrice(),
                    'formattedPrice' => $article->getPrice() ? '$' . number_format($article->getPrice(), 2) : 'Free',
                    'sku' => $article->getSku(),
                ];
            }
        }

        return array_slice($relatedArticles, 0, 3); // Limit to 3 related articles
    }
}
