<?php

declare(strict_types=1);

namespace App\Application\Actions\Pages;

use App\Application\Actions\TemplateAction;
use App\Domain\User\UserRepository;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;

/**
 * Users Page Action
 *
 * Renders the users listing page using Plates templates
 */
class UsersPageAction extends TemplateAction
{
    private UserRepository $userRepository;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        $this->userRepository = new UserRepository($this->entityManager);
    }

    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        $users = $this->userRepository->findAllUsers();

        // Transform users for template display
        $usersData = array_map(function ($user) {
            return [
                'id' => $user->getId(),
                'username' => $user->getUsername(),
                'firstName' => $user->getFirstName(),
                'lastName' => $user->getLastName(),
                'email' => $user->getUsername() . '@example.com', // Mock email
                'fullName' => $user->getFirstName() . ' ' . $user->getLastName(),
                'initials' => substr($user->getFirstName(), 0, 1) . substr($user->getLastName(), 0, 1),
            ];
        }, $users);

        $data = [
            'title' => 'Users',
            'users' => $usersData,
            'totalUsers' => count($usersData),
            'apiEndpoint' => '/api/v1/users',
        ];

        $this->logger->info('Users page accessed', [
            'user_count' => count($usersData),
            'ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
        ]);

        return $this->render('users::index', $data);
    }
}
