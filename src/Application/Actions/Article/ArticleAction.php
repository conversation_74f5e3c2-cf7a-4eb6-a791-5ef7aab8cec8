<?php

declare(strict_types=1);

namespace App\Application\Actions\Article;

use App\Application\Actions\Action;
use App\Domain\Article\ArticleRepository;
use Doctrine\ORM\EntityManager;
use Psr\Container\ContainerInterface;

abstract class ArticleAction extends Action
{
    protected ArticleRepository $articleRepository;
    protected EntityManager $articleEntityManager;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);

        // Get the Article-specific EntityManager
        $this->articleEntityManager = $container->get('ArticleEntityManager');
        $this->articleRepository = new ArticleRepository($this->articleEntityManager);
    }
}
