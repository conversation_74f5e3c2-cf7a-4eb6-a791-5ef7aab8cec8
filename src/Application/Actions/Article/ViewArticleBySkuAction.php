<?php

declare(strict_types=1);

namespace App\Application\Actions\Article;

use App\Domain\Article\ArticleNotFoundException;
use Psr\Http\Message\ResponseInterface as Response;

class ViewArticleBySkuAction extends ArticleAction
{
    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        $sku = (string) $this->resolveArg('sku');

        $article = $this->articleRepository->findArticleBySku($sku);

        if (!$article) {
            throw new ArticleNotFoundException();
        }

        $this->logger->info('Article with SKU ' . $sku . ' was viewed.', [
            'sku' => $sku,
            'articleId' => $article->getId(),
            'articleTitle' => $article->getTitle(),
            'articleType' => $article->getType()->value
        ]);

        return $this->respondWithData($article);
    }
}
