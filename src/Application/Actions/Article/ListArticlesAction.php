<?php

declare(strict_types=1);

namespace App\Application\Actions\Article;

use Psr\Http\Message\ResponseInterface as Response;

class ListArticlesAction extends ArticleAction
{
    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        // Get query parameters for filtering
        $queryParams = $this->request->getQueryParams();
        
        $type = $queryParams['type'] ?? null;
        $inStock = isset($queryParams['in_stock']) ? (bool) $queryParams['in_stock'] : null;
        $search = $queryParams['search'] ?? null;
        $page = (int) ($queryParams['page'] ?? 1);
        $limit = (int) ($queryParams['limit'] ?? 10);

        // Apply filters based on query parameters
        if ($search) {
            $articles = $this->articleRepository->searchArticles($search);
        } elseif ($type) {
            try {
                $articleType = \App\Domain\Article\ArticleType::from($type);
                $articles = $this->articleRepository->findArticlesByType($articleType);
            } catch (\ValueError $e) {
                $articles = [];
            }
        } elseif ($inStock) {
            $articles = $this->articleRepository->findInStockArticles();
        } else {
            // Use pagination for general listing
            $articles = $this->articleRepository->findArticlesPaginated($page, $limit);
        }

        $this->logger->info("Articles list was viewed.", [
            'filters' => [
                'type' => $type,
                'inStock' => $inStock,
                'search' => $search,
                'page' => $page,
                'limit' => $limit
            ],
            'resultCount' => count($articles)
        ]);

        return $this->respondWithData([
            'articles' => $articles,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $this->articleRepository->getTotalActiveCount()
            ],
            'filters' => [
                'type' => $type,
                'inStock' => $inStock,
                'search' => $search
            ]
        ]);
    }
}
