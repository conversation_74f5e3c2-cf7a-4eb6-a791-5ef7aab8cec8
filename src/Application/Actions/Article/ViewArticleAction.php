<?php

declare(strict_types=1);

namespace App\Application\Actions\Article;

use App\Domain\Article\ArticleNotFoundException;
use Psr\Http\Message\ResponseInterface as Response;

class ViewArticleAction extends ArticleAction
{
    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        $articleId = (int) $this->resolveArg('id');

        $article = $this->articleRepository->findArticleOfId($articleId);

        if (!$article) {
            throw new ArticleNotFoundException();
        }

        $this->logger->info('Article of id ' . $articleId . ' was viewed.', [
            'articleId' => $articleId,
            'articleTitle' => $article->getTitle(),
            'articleType' => $article->getType()->value
        ]);

        return $this->respondWithData($article);
    }
}
