<?php

declare(strict_types=1);

namespace App\Application\Actions\Test;

use App\Application\Actions\Action;
use Psr\Http\Message\ResponseInterface as Response;

/**
 * Telegram Logger Test Action
 *
 * Test action to verify Telegram error logging functionality.
 * This action generates different types of log messages to test the Telegram handler.
 */
class TelegramLoggerTestAction extends Action
{
    /**
     * {@inheritdoc}
     */
    protected function action(): Response
    {
        $testType = (string) ($this->resolveArg('type') ?? 'info');

        switch ($testType) {
            case 'error':
                $this->testError();
                break;
            case 'warning':
                $this->testWarning();
                break;
            case 'critical':
                $this->testCritical();
                break;
            case 'exception':
                $this->testException();
                break;
            case 'context':
                $this->testWithContext();
                break;
            default:
                $this->testInfo();
                break;
        }

        return $this->respondWithData([
            'message' => "Telegram logger test '{$testType}' executed",
            'timestamp' => date('Y-m-d H:i:s'),
            'note' => 'Check your Telegram channel for the log message',
        ]);
    }

    /**
     * Test info level logging
     */
    private function testInfo(): void
    {
        $this->logger->info('Telegram Logger Test - Info Level', [
            'appName' => 'Slim4 Test App',
            'test_type' => 'info',
            'user_ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
        ]);
    }

    /**
     * Test error level logging
     */
    private function testError(): void
    {
        $this->logger->error('Telegram Logger Test - Error Level', [
            'appName' => 'Slim4 Test App',
            'test_type' => 'error',
            'error_code' => 'TEST_ERROR_001',
            'user_ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
        ]);
    }

    /**
     * Test warning level logging
     */
    private function testWarning(): void
    {
        $this->logger->warning('Telegram Logger Test - Warning Level', [
            'appName' => 'Slim4 Test App',
            'test_type' => 'warning',
            'warning_code' => 'TEST_WARNING_001',
            'user_ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
        ]);
    }

    /**
     * Test critical level logging
     */
    private function testCritical(): void
    {
        $this->logger->critical('Telegram Logger Test - Critical Level', [
            'appName' => 'Slim4 Test App',
            'test_type' => 'critical',
            'critical_code' => 'TEST_CRITICAL_001',
            'system_status' => 'degraded',
            'user_ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
        ]);
    }

    /**
     * Test exception logging
     */
    private function testException(): void
    {
        try {
            throw new \RuntimeException('This is a test exception for Telegram logging');
        } catch (\Exception $e) {
            $this->logger->error('Telegram Logger Test - Exception Caught', [
                'appName' => 'Slim4 Test App',
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => substr($e->getTraceAsString(), 0, 500), // Limit trace length
                'user_ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
            ]);
        }
    }

    /**
     * Test logging with rich context
     */
    private function testWithContext(): void
    {
        $this->logger->error('Telegram Logger Test - Rich Context', [
            'appName' => 'Slim4 Test App',
            'test_type' => 'context',
            'user_id' => 12345,
            'action' => 'telegram_test',
            'request_method' => $this->request->getMethod(),
            'request_uri' => (string) $this->request->getUri(),
            'user_agent' => $this->request->getHeaderLine('User-Agent'),
            'user_ip' => $this->request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
        ]);
    }
}
