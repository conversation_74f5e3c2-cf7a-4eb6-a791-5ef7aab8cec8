<?php

declare(strict_types=1);

namespace App\Application\Middleware;

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\MiddlewareInterface as Middleware;
use Psr\Http\Server\RequestHandlerInterface as RequestHandler;
use ResponsiveSk\Slim4Paths\Paths;
use Slim\Exception\HttpBadRequestException;

/**
 * Path Security Middleware
 * 
 * Protects against path traversal attacks (../, ..\, etc.)
 * and ensures all file paths are within allowed directories
 */
class PathSecurityMiddleware implements Middleware
{
    private Paths $paths;
    private array $allowedDirectories;
    private array $blockedPatterns;

    public function __construct(Paths $paths)
    {
        $this->paths = $paths;
        
        // Define allowed base directories
        $this->allowedDirectories = [
            $this->paths->public(),
            $this->paths->uploads(),
            $this->paths->assets(),
            $this->paths->templates(),
            $this->paths->storage(),
        ];

        // Define blocked patterns for path traversal attacks
        $this->blockedPatterns = [
            '../',      // Unix path traversal
            '..\\',     // Windows path traversal
            '..%2f',    // URL encoded Unix
            '..%2F',    // URL encoded Unix (uppercase)
            '..%5c',    // URL encoded Windows
            '..%5C',    // URL encoded Windows (uppercase)
            '%2e%2e%2f', // Double URL encoded Unix
            '%2e%2e%5c', // Double URL encoded Windows
            '..../',    // Double dot bypass attempt
            '....\\',   // Double dot bypass attempt (Windows)
            '.%2e/',    // Mixed encoding
            '.%2e\\',   // Mixed encoding (Windows)
        ];
    }

    /**
     * Process the middleware
     */
    public function process(Request $request, RequestHandler $handler): Response
    {
        // Check query parameters for path traversal
        $queryParams = $request->getQueryParams();
        $this->validateParameters($queryParams, $request);

        // Check POST data for path traversal
        $parsedBody = $request->getParsedBody();
        if (is_array($parsedBody)) {
            $this->validateParameters($parsedBody, $request);
        }

        // Check route arguments for path traversal
        $routeArgs = $request->getAttribute('route')?->getArguments() ?? [];
        $this->validateParameters($routeArgs, $request);

        return $handler->handle($request);
    }

    /**
     * Validate parameters for path traversal attacks
     */
    private function validateParameters(array $parameters, Request $request): void
    {
        foreach ($parameters as $key => $value) {
            if (is_string($value)) {
                $this->validatePath($value, $key, $request);
            } elseif (is_array($value)) {
                $this->validateParameters($value, $request);
            }
        }
    }

    /**
     * Validate a single path value
     */
    private function validatePath(string $path, string $paramName, Request $request): void
    {
        // Check for blocked patterns
        $lowercasePath = strtolower($path);
        foreach ($this->blockedPatterns as $pattern) {
            if (str_contains($lowercasePath, strtolower($pattern))) {
                throw new HttpBadRequestException(
                    $request,
                    "Path traversal attempt detected in parameter '{$paramName}': {$pattern}"
                );
            }
        }

        // If the path looks like a file path, validate it's within allowed directories
        if ($this->looksLikeFilePath($path)) {
            $this->validateFilePathSecurity($path, $paramName, $request);
        }
    }

    /**
     * Check if a string looks like a file path
     */
    private function looksLikeFilePath(string $path): bool
    {
        return str_contains($path, '/') || 
               str_contains($path, '\\') || 
               str_contains($path, '.') ||
               preg_match('/\.(php|html|css|js|jpg|png|gif|pdf|txt|log)$/i', $path);
    }

    /**
     * Validate file path security using realpath
     */
    private function validateFilePathSecurity(string $path, string $paramName, Request $request): void
    {
        // Convert relative path to absolute path
        $absolutePath = $this->paths->path($path);
        
        // Use realpath to resolve any .. or . components
        $realPath = realpath($absolutePath);
        
        // If realpath returns false, the path doesn't exist or is invalid
        if ($realPath === false) {
            // For non-existent files, check the directory structure
            $realPath = $this->validateNonExistentPath($absolutePath, $paramName, $request);
        }

        // Check if the resolved path is within allowed directories
        $isAllowed = false;
        foreach ($this->allowedDirectories as $allowedDir) {
            $realAllowedDir = realpath($allowedDir);
            if ($realAllowedDir !== false && str_starts_with($realPath, $realAllowedDir)) {
                $isAllowed = true;
                break;
            }
        }

        if (!$isAllowed) {
            throw new HttpBadRequestException(
                $request,
                "Access denied: Path '{$path}' in parameter '{$paramName}' is outside allowed directories"
            );
        }
    }

    /**
     * Validate non-existent path by checking its directory
     */
    private function validateNonExistentPath(string $absolutePath, string $paramName, Request $request): string
    {
        $directory = dirname($absolutePath);
        $realDirectory = realpath($directory);
        
        if ($realDirectory === false) {
            throw new HttpBadRequestException(
                $request,
                "Invalid path: Directory for '{$absolutePath}' in parameter '{$paramName}' does not exist"
            );
        }

        // Return the real directory path + filename
        return $realDirectory . DIRECTORY_SEPARATOR . basename($absolutePath);
    }

    /**
     * Add custom allowed directory
     */
    public function addAllowedDirectory(string $directory): self
    {
        $realDir = realpath($directory);
        if ($realDir !== false) {
            $this->allowedDirectories[] = $realDir;
        }
        return $this;
    }

    /**
     * Add custom blocked pattern
     */
    public function addBlockedPattern(string $pattern): self
    {
        $this->blockedPatterns[] = $pattern;
        return $this;
    }

    /**
     * Get current allowed directories
     */
    public function getAllowedDirectories(): array
    {
        return $this->allowedDirectories;
    }

    /**
     * Get current blocked patterns
     */
    public function getBlockedPatterns(): array
    {
        return $this->blockedPatterns;
    }
}
