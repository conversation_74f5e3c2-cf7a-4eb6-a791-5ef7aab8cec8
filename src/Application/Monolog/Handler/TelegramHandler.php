<?php

declare(strict_types=1);

namespace App\Application\Monolog\Handler;

use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Level;
use Monolog\LogRecord;

/**
 * Telegram Error Logger Handler
 *
 * Sends log messages to Telegram channel/chat for real-time error monitoring.
 * Based on SlimCore TelegramHandler with improvements for our project.
 */
class TelegramHandler extends AbstractProcessingHandler
{
    protected string $token;
    protected string $channel;
    protected string $dateFormat;
    protected string $appName;

    /**
     * @var array<int, mixed>
     */
    protected array $curlOptions;

    private const string HOST = 'https://api.telegram.org/bot';

    /**
     * @param string $token Telegram Bot Access Token Provided by BotFather
     * @param string $channel Telegram Channel userName or chat ID
     * @param string $appName Application name for message identification
     * @param Level $level Minimum log level
     * @param bool $bubble Whether to bubble up to next handler
     */
    public function __construct(
        string $token,
        string $channel,
        string $appName = 'Slim4 App',
        Level $level = Level::Error,
        bool $bubble = true
    ) {
        parent::__construct($level, $bubble);

        $this->token = $token;
        $this->channel = $channel;
        $this->appName = $appName;
        $this->dateFormat = 'Y-m-d H:i:s';
        $this->curlOptions = [
            CURLOPT_TIMEOUT => 5,
            CURLOPT_CONNECTTIMEOUT => 3,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function write(array|LogRecord $record): void
    {
        if ($record instanceof LogRecord) {
            $record = $record->toArray();
        }

        $message = $this->formatMessage($record);
        $this->sendToTelegram($message);
    }

    /**
     * Format log message for Telegram
     */
    protected function formatMessage(array $record): string
    {
        $emoji = $this->getEmoji($record['level']);
        $appName = $record['context']['appName'] ?? $this->appName;
        $timestamp = date($this->dateFormat);

        $message = "{$emoji} *{$appName}* - {$record['level_name']}\n";
        $message .= "🕐 {$timestamp}\n\n";
        $message .= "📝 *Message:*\n{$record['message']}\n";

        // Add context information if available
        if (!empty($record['context'])) {
            $context = $this->formatContext($record['context']);
            if ($context) {
                $message .= "\n📋 *Context:*\n{$context}";
            }
        }

        // Add extra information if available
        if (!empty($record['extra'])) {
            $extra = $this->formatExtra($record['extra']);
            if ($extra) {
                $message .= "\n🔧 *Extra:*\n{$extra}";
            }
        }

        return $message;
    }

    /**
     * Format context data for display
     */
    protected function formatContext(array $context): string
    {
        $formatted = [];

        foreach ($context as $key => $value) {
            if ($key === 'appName') {
                continue; // Already used in header
            }

            if (is_scalar($value)) {
                $formatted[] = "• {$key}: {$value}";
            } elseif (is_array($value)) {
                $formatted[] = "• {$key}: " . json_encode($value, JSON_UNESCAPED_UNICODE);
            } else {
                $formatted[] = "• {$key}: " . gettype($value);
            }
        }

        return implode("\n", array_slice($formatted, 0, 5)); // Limit to 5 items
    }

    /**
     * Format extra data for display
     */
    protected function formatExtra(array $extra): string
    {
        $formatted = [];

        foreach ($extra as $key => $value) {
            if (is_scalar($value)) {
                $formatted[] = "• {$key}: {$value}";
            }
        }

        return implode("\n", array_slice($formatted, 0, 3)); // Limit to 3 items
    }

    /**
     * Send message to Telegram
     */
    protected function sendToTelegram(string $message): void
    {
        $ch = curl_init();
        $url = self::HOST . $this->token . "/sendMessage";

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query([
                'text' => $message,
                'chat_id' => $this->channel,
                'parse_mode' => 'Markdown',
                'disable_web_page_preview' => true,
            ]),
        ]);

        // Apply custom curl options
        foreach ($this->curlOptions as $option => $value) {
            curl_setopt($ch, $option, $value);
        }

        try {
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if ($response === false || $httpCode !== 200) {
                // Log curl error to system log as fallback
                error_log("TelegramHandler: Failed to send message. HTTP Code: {$httpCode}, Error: " . curl_error($ch));
            }
        } catch (\Exception $e) {
            // Silently fail to prevent infinite loops
            error_log("TelegramHandler: Exception occurred: " . $e->getMessage());
        } finally {
            curl_close($ch);
        }
    }

    /**
     * Get emoji map for different log levels
     */
    protected function emojiMap(): array
    {
        return [
            Level::Debug->value => '🐛',
            Level::Info->value => 'ℹ️',
            Level::Notice->value => '📢',
            Level::Warning->value => '⚠️',
            Level::Error->value => '❌',
            Level::Critical->value => '🚨',
            Level::Alert->value => '🔥',
            Level::Emergency->value => '💥',
        ];
    }

    /**
     * Get emoji for log level
     */
    protected function getEmoji(int $level): string
    {
        $levelEmojiMap = $this->emojiMap();
        return $levelEmojiMap[$level] ?? '📝';
    }

    /**
     * Set custom curl options
     */
    public function setCurlOptions(array $options): self
    {
        $this->curlOptions = array_merge($this->curlOptions, $options);
        return $this;
    }

    /**
     * Set application name
     */
    public function setAppName(string $appName): self
    {
        $this->appName = $appName;
        return $this;
    }
}
