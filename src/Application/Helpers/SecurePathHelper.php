<?php

declare(strict_types=1);

namespace App\Application\Helpers;

use ResponsiveSk\Slim4Paths\Paths;
use InvalidArgumentException;

/**
 * Secure Path Helper
 *
 * Provides secure path operations with built-in protection against
 * path traversal attacks and directory restrictions
 */
class SecurePathHelper
{
    private Paths $paths;
    private array $allowedDirectories;

    public function __construct(Paths $paths)
    {
        $this->paths = $paths;

        // Define allowed base directories using Paths methods
        $this->allowedDirectories = [
            'public' => $this->paths->public(),
            'uploads' => $this->paths->uploads(),
            'assets' => $this->paths->assets(),
            'templates' => $this->paths->templates(),
            'storage' => $this->paths->storage(),
            'cache' => $this->paths->cache(),
            'logs' => $this->paths->logs(),
            'config' => $this->paths->config(),
        ];
    }

    /**
     * Securely resolve a file path within allowed directories
     *
     * @param string $relativePath The relative path to resolve
     * @param string $baseDirectory The base directory name (public, uploads, etc.)
     * @return string The secure absolute path
     * @throws InvalidArgumentException If path is invalid or outside allowed directories
     */
    public function securePath(string $relativePath, string $baseDirectory = 'public'): string
    {
        // Validate base directory
        if (!isset($this->allowedDirectories[$baseDirectory])) {
            throw new InvalidArgumentException("Base directory '{$baseDirectory}' is not allowed");
        }

        // Check for path traversal patterns
        $this->validatePathTraversal($relativePath);

        // Get the base directory path
        $basePath = $this->allowedDirectories[$baseDirectory];

        // Combine paths
        $fullPath = $basePath . DIRECTORY_SEPARATOR . ltrim($relativePath, '/\\');

        // Normalize the path
        $normalizedPath = $this->normalizePath($fullPath);

        // Use realpath to resolve any remaining .. or . components
        $realPath = realpath($normalizedPath);

        // If file doesn't exist, validate the directory structure
        if ($realPath === false) {
            $realPath = $this->validateNonExistentPath($normalizedPath, $basePath);
        }

        // Ensure the resolved path is still within the allowed directory
        $realBasePath = realpath($basePath);
        if ($realBasePath === false || !str_starts_with($realPath, $realBasePath)) {
            throw new InvalidArgumentException(
                "Path '{$relativePath}' resolves outside the allowed directory '{$baseDirectory}'"
            );
        }

        return $realPath;
    }

    /**
     * Check if a file exists securely
     */
    public function fileExists(string $relativePath, string $baseDirectory = 'public'): bool
    {
        try {
            $securePath = $this->securePath($relativePath, $baseDirectory);
            return file_exists($securePath);
        } catch (InvalidArgumentException) {
            return false;
        }
    }

    /**
     * Read file contents securely
     */
    public function readFile(string $relativePath, string $baseDirectory = 'public'): string
    {
        $securePath = $this->securePath($relativePath, $baseDirectory);

        if (!file_exists($securePath)) {
            throw new InvalidArgumentException("File '{$relativePath}' does not exist");
        }

        if (!is_readable($securePath)) {
            throw new InvalidArgumentException("File '{$relativePath}' is not readable");
        }

        $content = file_get_contents($securePath);
        if ($content === false) {
            throw new InvalidArgumentException("Failed to read file '{$relativePath}'");
        }

        return $content;
    }

    /**
     * Write file contents securely
     */
    public function writeFile(string $relativePath, string $content, string $baseDirectory = 'storage'): bool
    {
        $securePath = $this->securePath($relativePath, $baseDirectory);

        // Ensure directory exists
        $directory = dirname($securePath);
        if (!is_dir($directory)) {
            if (!mkdir($directory, 0755, true)) {
                throw new InvalidArgumentException("Failed to create directory for '{$relativePath}'");
            }
        }

        return file_put_contents($securePath, $content) !== false;
    }

    /**
     * Get secure URL for public files
     */
    public function getPublicUrl(string $relativePath): string
    {
        // Validate the path is in public directory
        $securePath = $this->securePath($relativePath, 'public');

        // Convert absolute path back to relative URL
        $publicPath = $this->allowedDirectories['public'];
        $relativePath = str_replace($publicPath, '', $securePath);
        $relativePath = str_replace(DIRECTORY_SEPARATOR, '/', $relativePath);

        return '/' . ltrim($relativePath, '/');
    }

    /**
     * List files in directory securely
     */
    public function listFiles(string $relativePath = '', string $baseDirectory = 'public'): array
    {
        $securePath = $this->securePath($relativePath, $baseDirectory);

        if (!is_dir($securePath)) {
            throw new InvalidArgumentException("Directory '{$relativePath}' does not exist");
        }

        $files = [];
        $iterator = new \DirectoryIterator($securePath);

        foreach ($iterator as $fileInfo) {
            if ($fileInfo->isDot()) {
                continue;
            }

            $files[] = [
                'name' => $fileInfo->getFilename(),
                'type' => $fileInfo->isDir() ? 'directory' : 'file',
                'size' => $fileInfo->isFile() ? $fileInfo->getSize() : null,
                'modified' => $fileInfo->getMTime(),
            ];
        }

        return $files;
    }

    /**
     * Validate path for traversal attacks
     */
    private function validatePathTraversal(string $path): void
    {
        $dangerousPatterns = [
            '../',
            '..\\',
            '..%2f',
            '..%2F',
            '..%5c',
            '..%5C',
            '%2e%2e%2f',
            '%2e%2e%5c',
            '..../',
            '....\\',
        ];

        $lowercasePath = strtolower($path);
        foreach ($dangerousPatterns as $pattern) {
            if (str_contains($lowercasePath, strtolower($pattern))) {
                throw new InvalidArgumentException("Path traversal attempt detected: {$pattern}");
            }
        }
    }

    /**
     * Normalize path separators and remove redundant components
     */
    private function normalizePath(string $path): string
    {
        // Convert all separators to system separator
        $path = str_replace(['/', '\\'], DIRECTORY_SEPARATOR, $path);

        // Remove duplicate separators
        $normalizedPath = preg_replace('#' . preg_quote(DIRECTORY_SEPARATOR) . '+#', DIRECTORY_SEPARATOR, $path);

        return $normalizedPath ?? $path;
    }

    /**
     * Validate non-existent path
     */
    private function validateNonExistentPath(string $fullPath, string $basePath): string
    {
        $directory = dirname($fullPath);
        $realDirectory = realpath($directory);

        if ($realDirectory === false) {
            throw new InvalidArgumentException("Directory does not exist: {$directory}");
        }

        $realBasePath = realpath($basePath);
        if ($realBasePath === false || !str_starts_with($realDirectory, $realBasePath)) {
            throw new InvalidArgumentException("Path resolves outside allowed directory");
        }

        return $realDirectory . DIRECTORY_SEPARATOR . basename($fullPath);
    }

    /**
     * Add custom allowed directory
     */
    public function addAllowedDirectory(string $name, string $path): self
    {
        $realPath = realpath($path);
        if ($realPath !== false) {
            $this->allowedDirectories[$name] = $realPath;
        }
        return $this;
    }

    /**
     * Get allowed directories
     */
    public function getAllowedDirectories(): array
    {
        return $this->allowedDirectories;
    }
}
