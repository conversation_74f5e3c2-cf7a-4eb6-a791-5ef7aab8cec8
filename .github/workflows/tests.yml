name: Tests

on: 
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
    tests:
        name: Tests PHP ${{ matrix.php }}
        runs-on: ubuntu-latest
        continue-on-error: ${{ matrix.experimental }}
        strategy:
            fail-fast: false
            matrix:
                php: [ 8.2, 8.3]
                experimental: [false]
                include:
                    - php: 8.3
                      analysis: true

        steps:
            - name: Checkout
              uses: actions/checkout@v2

            - name: Set up PHP ${{ matrix.php }}
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ matrix.php }}
                  coverage: xdebug

            - name: Validate composer.json
              run: composer validate --strict --no-check-lock

            - name: Install dependencies with Composer
              uses: ramsey/composer-install@v2

            - name: Coding standards
              if: matrix.analysis
              run: vendor/bin/phpcs

            - name: Static analysis
              if: matrix.analysis
              run: vendor/bin/phpstan

            - name: Doctrine database
              run:  |
                php ./bin/doctrine.php orm:schema-tool:update --dump-sql
                php ./bin/doctrine.php orm:schema-tool:update --force

            - name: Tests
              #run: vendor/bin/phpunit --coverage-clover clover.xml
              run: vendor/bin/phpunit

            # - name: Upload coverage results to Coveralls
            #   if: matrix.analysis
            #   env:
            #       COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
            #   run: |
            #       composer require php-coveralls/php-coveralls -n -W
            #       vendor/bin/php-coveralls --coverage_clover=clover.xml -v
