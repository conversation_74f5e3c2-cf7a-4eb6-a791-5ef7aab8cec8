# Environment Configuration Example
# Copy this file to .env and configure your values

# Application Environment
APP_ENV=development
APP_DEBUG=true

# Telegram Error Logger Configuration
# To enable Telegram error logging, configure these values:

# 1. Create a Telegram bot:
#    - Message @BotFather on Telegram
#    - Send /newbot command
#    - Follow instructions to create your bot
#    - Copy the bot token below

TELEGRAM_BOT_TOKEN=

# 2. Get your chat/channel ID:
#    - For personal chat: Message your bot, then visit:
#      https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
#    - For channel: Add bot as admin, then get channel ID
#    - Use the chat ID (number) or channel username (@channel_name)

TELEGRAM_CHANNEL=

# 3. Application name for Telegram messages
TELEGRAM_APP_NAME="Slim4 App"

# Database Configuration (if needed)
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=slim4_app
# DB_USER=root
# DB_PASS=

# Other Configuration
# CACHE_DRIVER=file
# SESSION_DRIVER=file
