<?xml version="1.0"?>
<ruleset name="Slim coding standard">
    <description>Slim coding standard</description>

    <!-- display progress -->
    <arg value="p"/>
    <!-- use colors in output -->
    <arg name="colors"/>

    <!-- inherit rules from: -->
    <rule ref="PSR12"/>
    <rule ref="Generic.Arrays.DisallowLongArraySyntax"/>

    <!-- Additional rules -->
    <rule ref="Generic.PHP.ForbiddenFunctions">
        <properties>
            <property name="forbiddenFunctions" type="array">
                <element key="var_dump" value="null"/>
                <element key="print_r" value="null"/>
                <element key="die" value="exit"/>
            </property>
        </properties>
    </rule>

    <!-- Exclude patterns -->
    <exclude-pattern>*/vendor/*</exclude-pattern>
    <exclude-pattern>*/var/*</exclude-pattern>
    <exclude-pattern>*/logs/*</exclude-pattern>

    <!-- Paths to check -->
    <file>src</file>
    <file>tests</file>
    <file>app</file>
    <file>bin</file>

    <!-- Exclude specific files -->
    <exclude-pattern>tests/bootstrap.php</exclude-pattern>
</ruleset>