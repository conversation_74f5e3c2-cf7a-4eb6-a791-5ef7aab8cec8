# Environment Configuration
# This file contains environment-specific settings

# Application Environment
APP_ENV=development
APP_DEBUG=true

# Telegram Error Logger Configuration
# To enable Telegram error logging, configure these values:

# Leave empty to disable Telegram logging (will only log to files)
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHANNEL=
TELEGRAM_APP_NAME="Slim4 Development App"

# Database Configuration (if needed in future)
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=slim4_app
# DB_USER=root
# DB_PASS=

# Other Configuration
# CACHE_DRIVER=file
# SESSION_DRIVER=file
