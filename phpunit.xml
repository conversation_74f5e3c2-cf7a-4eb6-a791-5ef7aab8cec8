<phpunit
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://schema.phpunit.de/7.1/phpunit.xsd"
    backupGlobals="false"
    backupStaticAttributes="false"
    beStrictAboutTestsThatDoNotTestAnything="true"
    beStrictAboutChangesToGlobalState="true"
    beStrictAboutOutputDuringTests="true"
    colors="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    processIsolation="false"
    stopOnFailure="false"
    bootstrap="tests/bootstrap.php"
>
    <testsuites>
        <testsuite name="Test Suite">
            <directory>./tests/</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./src/</directory>
        </whitelist>
    </filter>
</phpunit>
